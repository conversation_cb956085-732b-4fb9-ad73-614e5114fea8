import tkinter as tk
from tkinter import ttk, messagebox, simpledialog

class CategoriesWindow:
    """نافذة إدارة الفئات"""
    
    def __init__(self, parent, data_manager):
        self.parent = parent
        self.data_manager = data_manager
        
        # إنشاء النافذة
        self.window = tk.Toplevel(parent)
        self.window.title("إدارة الفئات")
        self.window.geometry("600x500")
        self.window.configure(bg='#f8f9fa')
        
        # منع إغلاق النافذة الرئيسية
        self.window.transient(parent)
        
        self.setup_ui()
        self.load_categories()
        self.center_window()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان
        title_frame = tk.Frame(self.window, bg='#f8f9fa')
        title_frame.pack(pady=20)
        
        title_label = tk.Label(
            title_frame,
            text="إدارة فئات المنتجات",
            font=("Arial", 18, "bold"),
            bg='#f8f9fa',
            fg='#2c3e50'
        )
        title_label.pack()
        
        # إطار الجدول
        table_frame = tk.Frame(self.window, bg='#f8f9fa')
        table_frame.pack(pady=20, padx=20, fill=tk.BOTH, expand=True)
        
        # إنشاء الجدول
        columns = ("اختيار", "اسم الفئة")
        self.tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)
        
        # تعريف أعمدة الجدول
        self.tree.heading("اختيار", text="اختيار")
        self.tree.heading("اسم الفئة", text="اسم الفئة")
        
        # تحديد عرض الأعمدة
        self.tree.column("اختيار", width=80, anchor=tk.CENTER)
        self.tree.column("اسم الفئة", width=300, anchor=tk.CENTER)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # ترتيب الجدول وشريط التمرير
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط النقر المزدوج بالتعديل
        self.tree.bind("<Double-1>", lambda event: self.edit_selected_category())
        
        # إطار الأزرار
        buttons_frame = tk.Frame(self.window, bg='#f8f9fa')
        buttons_frame.pack(pady=20)
        
        # زر إضافة فئة
        add_btn = tk.Button(
            buttons_frame,
            text="إضافة فئة",
            command=self.add_category,
            bg='#27ae60',
            fg='white',
            font=("Arial", 12, "bold"),
            width=12,
            height=2,
            cursor="hand2"
        )
        add_btn.pack(side=tk.LEFT, padx=10)
        
        # زر تعديل فئة
        edit_btn = tk.Button(
            buttons_frame,
            text="تعديل فئة",
            command=self.edit_selected_category,
            bg='#3498db',
            fg='white',
            font=("Arial", 12, "bold"),
            width=12,
            height=2,
            cursor="hand2"
        )
        edit_btn.pack(side=tk.LEFT, padx=10)
        
        # زر حذف فئة
        delete_btn = tk.Button(
            buttons_frame,
            text="حذف فئة",
            command=self.delete_selected_category,
            bg='#e74c3c',
            fg='white',
            font=("Arial", 12, "bold"),
            width=12,
            height=2,
            cursor="hand2"
        )
        delete_btn.pack(side=tk.LEFT, padx=10)
        
        # زر الإغلاق
        close_btn = tk.Button(
            buttons_frame,
            text="إغلاق",
            command=self.window.destroy,
            bg='#95a5a6',
            fg='white',
            font=("Arial", 12, "bold"),
            width=12,
            height=2,
            cursor="hand2"
        )
        close_btn.pack(side=tk.LEFT, padx=10)
        
        # شريط الحالة
        self.status_bar = tk.Label(
            self.window,
            text="جاهز",
            relief=tk.SUNKEN,
            anchor=tk.W,
            bg='#ecf0f1',
            fg='#2c3e50'
        )
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
    
    def load_categories(self):
        """تحميل الفئات"""
        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # تحميل الفئات
            self.categories = self.data_manager.categories.get_all_categories()
            
            # عرض البيانات
            for i, category in enumerate(self.categories):
                # إضافة رقم تسلسلي كمؤشر للاختيار
                self.tree.insert("", tk.END, values=(i+1, category["name"]), tags=(category["id"],))
            
            # تحديث شريط الحالة
            self.status_bar.config(text=f"تم تحميل {len(self.categories)} فئة")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل الفئات: {str(e)}")
    
    def get_selected_category(self):
        """الحصول على الفئة المحددة"""
        selection = self.tree.selection()
        if not selection:
            return None
        
        item = self.tree.item(selection[0])
        category_id = int(item['tags'][0])
        category_name = item['values'][1]
        
        return {"id": category_id, "name": category_name}
    
    def add_category(self):
        """إضافة فئة جديدة"""
        dialog = AddCategoryDialog(self.window, self.data_manager)
        if dialog.result:
            self.load_categories()
            self.status_bar.config(text=f"تم إضافة الفئة '{dialog.result}' بنجاح")
    
    def edit_selected_category(self):
        """تعديل الفئة المحددة"""
        selected = self.get_selected_category()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار فئة للتعديل")
            return
        
        dialog = EditCategoryDialog(self.window, self.data_manager, selected)
        if dialog.result:
            self.load_categories()
            self.status_bar.config(text=f"تم تعديل الفئة إلى '{dialog.result}' بنجاح")
    
    def delete_selected_category(self):
        """حذف الفئة المحددة"""
        selected = self.get_selected_category()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار فئة للحذف")
            return
        
        # تأكيد الحذف
        if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف الفئة '{selected['name']}'؟"):
            try:
                success = self.data_manager.categories.delete_category(selected["id"])
                if success:
                    self.load_categories()
                    self.status_bar.config(text=f"تم حذف الفئة '{selected['name']}' بنجاح")
                else:
                    messagebox.showerror("خطأ", "لا يمكن حذف الفئة لأنها مستخدمة في منتجات")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حذف الفئة: {str(e)}")
    
    def winfo_exists(self):
        """التحقق من وجود النافذة"""
        try:
            return self.window.winfo_exists()
        except:
            return False
    
    def lift(self):
        """رفع النافذة للمقدمة"""
        self.window.lift()
        self.window.focus_force()

class AddCategoryDialog:
    """نافذة حوار إضافة فئة جديدة"""
    
    def __init__(self, parent, data_manager):
        self.parent = parent
        self.data_manager = data_manager
        self.result = None
        
        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("إضافة فئة جديدة")
        self.dialog.geometry("400x200")
        self.dialog.configure(bg='#f8f9fa')
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        self.setup_ui()
        self.center_dialog()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان
        title_label = tk.Label(
            self.dialog,
            text="إضافة فئة جديدة",
            font=("Arial", 14, "bold"),
            bg='#f8f9fa',
            fg='#2c3e50'
        )
        title_label.pack(pady=20)
        
        # إطار الإدخال
        input_frame = tk.Frame(self.dialog, bg='#f8f9fa')
        input_frame.pack(pady=20)
        
        tk.Label(input_frame, text="اسم الفئة:", font=("Arial", 12), bg='#f8f9fa').pack()
        
        self.name_var = tk.StringVar()
        self.name_entry = tk.Entry(input_frame, textvariable=self.name_var, font=("Arial", 12), width=25)
        self.name_entry.pack(pady=10)
        self.name_entry.focus()
        
        # إطار الأزرار
        buttons_frame = tk.Frame(self.dialog, bg='#f8f9fa')
        buttons_frame.pack(pady=20)
        
        save_btn = tk.Button(
            buttons_frame,
            text="حفظ",
            command=self.save_category,
            bg='#27ae60',
            fg='white',
            font=("Arial", 12),
            width=10,
            cursor="hand2"
        )
        save_btn.pack(side=tk.LEFT, padx=10)
        
        cancel_btn = tk.Button(
            buttons_frame,
            text="إلغاء",
            command=self.dialog.destroy,
            bg='#e74c3c',
            fg='white',
            font=("Arial", 12),
            width=10,
            cursor="hand2"
        )
        cancel_btn.pack(side=tk.LEFT, padx=10)
        
        # ربط مفتاح Enter بالحفظ
        self.dialog.bind('<Return>', lambda event: self.save_category())
    
    def center_dialog(self):
        """توسيط النافذة"""
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f'{width}x{height}+{x}+{y}')
    
    def save_category(self):
        """حفظ الفئة الجديدة"""
        name = self.name_var.get().strip()
        
        if not name:
            messagebox.showerror("خطأ", "يرجى إدخال اسم الفئة")
            self.name_entry.focus()
            return
        
        try:
            success = self.data_manager.categories.add_category(name)
            if success:
                self.result = name
                self.dialog.destroy()
            else:
                messagebox.showerror("خطأ", "الفئة موجودة مسبقاً")
                self.name_entry.focus()
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إضافة الفئة: {str(e)}")

class EditCategoryDialog:
    """نافذة حوار تعديل فئة"""
    
    def __init__(self, parent, data_manager, category):
        self.parent = parent
        self.data_manager = data_manager
        self.category = category
        self.result = None
        
        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("تعديل الفئة")
        self.dialog.geometry("400x200")
        self.dialog.configure(bg='#f8f9fa')
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        self.setup_ui()
        self.center_dialog()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان
        title_label = tk.Label(
            self.dialog,
            text="تعديل الفئة",
            font=("Arial", 14, "bold"),
            bg='#f8f9fa',
            fg='#2c3e50'
        )
        title_label.pack(pady=20)
        
        # إطار الإدخال
        input_frame = tk.Frame(self.dialog, bg='#f8f9fa')
        input_frame.pack(pady=20)
        
        tk.Label(input_frame, text="اسم الفئة:", font=("Arial", 12), bg='#f8f9fa').pack()
        
        self.name_var = tk.StringVar()
        self.name_var.set(self.category["name"])
        self.name_entry = tk.Entry(input_frame, textvariable=self.name_var, font=("Arial", 12), width=25)
        self.name_entry.pack(pady=10)
        self.name_entry.focus()
        self.name_entry.select_range(0, tk.END)
        
        # إطار الأزرار
        buttons_frame = tk.Frame(self.dialog, bg='#f8f9fa')
        buttons_frame.pack(pady=20)
        
        save_btn = tk.Button(
            buttons_frame,
            text="حفظ",
            command=self.save_category,
            bg='#3498db',
            fg='white',
            font=("Arial", 12),
            width=10,
            cursor="hand2"
        )
        save_btn.pack(side=tk.LEFT, padx=10)
        
        cancel_btn = tk.Button(
            buttons_frame,
            text="إلغاء",
            command=self.dialog.destroy,
            bg='#e74c3c',
            fg='white',
            font=("Arial", 12),
            width=10,
            cursor="hand2"
        )
        cancel_btn.pack(side=tk.LEFT, padx=10)
        
        # ربط مفتاح Enter بالحفظ
        self.dialog.bind('<Return>', lambda event: self.save_category())
    
    def center_dialog(self):
        """توسيط النافذة"""
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f'{width}x{height}+{x}+{y}')
    
    def save_category(self):
        """حفظ التعديل"""
        name = self.name_var.get().strip()
        
        if not name:
            messagebox.showerror("خطأ", "يرجى إدخال اسم الفئة")
            self.name_entry.focus()
            return
        
        if name == self.category["name"]:
            self.dialog.destroy()
            return
        
        try:
            success = self.data_manager.categories.update_category(self.category["id"], name)
            if success:
                self.result = name
                self.dialog.destroy()
            else:
                messagebox.showerror("خطأ", "الفئة موجودة مسبقاً أو حدث خطأ في التحديث")
                self.name_entry.focus()
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تعديل الفئة: {str(e)}")

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime

class IntoStoreWindow:
    """نافذة إدخال البضائع إلى المتجر"""
    
    def __init__(self, parent, data_manager):
        self.parent = parent
        self.data_manager = data_manager
        
        # إنشاء النافذة
        self.window = tk.Toplevel(parent)
        self.window.title("إدخال البضائع")
        self.window.geometry("500x400")
        self.window.configure(bg='#f8f9fa')
        
        # منع إغلاق النافذة الرئيسية
        self.window.transient(parent)
        
        self.setup_ui()
        self.load_categories()
        self.center_window()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان
        title_frame = tk.Frame(self.window, bg='#f8f9fa')
        title_frame.pack(pady=20)
        
        title_label = tk.Label(
            title_frame,
            text="إدخال البضائع إلى المتجر",
            font=("Arial", 18, "bold"),
            bg='#f8f9fa',
            fg='#2c3e50'
        )
        title_label.pack()
        
        # إطار النموذج
        form_frame = tk.Frame(self.window, bg='#f8f9fa')
        form_frame.pack(pady=20, padx=40, fill=tk.BOTH, expand=True)
        
        # الباركود
        tk.Label(form_frame, text="الباركود:", font=("Arial", 12), bg='#f8f9fa').grid(
            row=0, column=0, sticky=tk.W, pady=10
        )
        
        self.barcode_var = tk.StringVar()
        self.barcode_entry = tk.Entry(
            form_frame, 
            textvariable=self.barcode_var,
            font=("Arial", 12),
            width=25
        )
        self.barcode_entry.grid(row=0, column=1, pady=10, padx=(10, 0), sticky=tk.W)
        self.barcode_entry.focus()
        
        # الفئة
        tk.Label(form_frame, text="الفئة:", font=("Arial", 12), bg='#f8f9fa').grid(
            row=1, column=0, sticky=tk.W, pady=10
        )
        
        self.category_var = tk.StringVar()
        self.category_combo = ttk.Combobox(
            form_frame,
            textvariable=self.category_var,
            font=("Arial", 12),
            width=22,
            state="readonly"
        )
        self.category_combo.grid(row=1, column=1, pady=10, padx=(10, 0), sticky=tk.W)
        
        # زر إضافة فئة جديدة
        add_category_btn = tk.Button(
            form_frame,
            text="إضافة فئة",
            command=self.add_new_category,
            bg='#9b59b6',
            fg='white',
            font=("Arial", 10),
            cursor="hand2"
        )
        add_category_btn.grid(row=1, column=2, pady=10, padx=(10, 0))
        
        # الكمية
        tk.Label(form_frame, text="الكمية:", font=("Arial", 12), bg='#f8f9fa').grid(
            row=2, column=0, sticky=tk.W, pady=10
        )
        
        self.quantity_var = tk.StringVar()
        self.quantity_entry = tk.Entry(
            form_frame,
            textvariable=self.quantity_var,
            font=("Arial", 12),
            width=25
        )
        self.quantity_entry.grid(row=2, column=1, pady=10, padx=(10, 0), sticky=tk.W)
        
        # التاريخ
        tk.Label(form_frame, text="التاريخ:", font=("Arial", 12), bg='#f8f9fa').grid(
            row=3, column=0, sticky=tk.W, pady=10
        )
        
        self.date_var = tk.StringVar()
        self.date_var.set(datetime.now().strftime("%Y-%m-%d"))
        self.date_entry = tk.Entry(
            form_frame,
            textvariable=self.date_var,
            font=("Arial", 12),
            width=25
        )
        self.date_entry.grid(row=3, column=1, pady=10, padx=(10, 0), sticky=tk.W)
        
        # إطار الأزرار
        buttons_frame = tk.Frame(self.window, bg='#f8f9fa')
        buttons_frame.pack(pady=30)
        
        # زر الحفظ
        save_btn = tk.Button(
            buttons_frame,
            text="حفظ",
            command=self.save_item,
            bg='#27ae60',
            fg='white',
            font=("Arial", 14, "bold"),
            width=12,
            height=2,
            cursor="hand2"
        )
        save_btn.pack(side=tk.LEFT, padx=10)
        
        # زر مسح النموذج
        clear_btn = tk.Button(
            buttons_frame,
            text="مسح",
            command=self.clear_form,
            bg='#f39c12',
            fg='white',
            font=("Arial", 14, "bold"),
            width=12,
            height=2,
            cursor="hand2"
        )
        clear_btn.pack(side=tk.LEFT, padx=10)
        
        # زر الإغلاق
        close_btn = tk.Button(
            buttons_frame,
            text="إغلاق",
            command=self.window.destroy,
            bg='#e74c3c',
            fg='white',
            font=("Arial", 14, "bold"),
            width=12,
            height=2,
            cursor="hand2"
        )
        close_btn.pack(side=tk.LEFT, padx=10)
        
        # ربط مفتاح Enter بالحفظ
        self.window.bind('<Return>', lambda event: self.save_item())
        
        # شريط الحالة
        self.status_bar = tk.Label(
            self.window,
            text="جاهز لإدخال البضائع",
            relief=tk.SUNKEN,
            anchor=tk.W,
            bg='#ecf0f1',
            fg='#2c3e50'
        )
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
    
    def load_categories(self):
        """تحميل الفئات"""
        try:
            categories = self.data_manager.categories.get_all_categories()
            category_names = [cat["name"] for cat in categories]
            self.category_combo['values'] = category_names
            
            if category_names:
                self.category_var.set(category_names[0])
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل الفئات: {str(e)}")
    
    def add_new_category(self):
        """إضافة فئة جديدة"""
        from categories_window import AddCategoryDialog
        dialog = AddCategoryDialog(self.window, self.data_manager)
        if dialog.result:
            self.load_categories()
            self.category_var.set(dialog.result)
    
    def validate_input(self):
        """التحقق من صحة البيانات المدخلة"""
        barcode = self.barcode_var.get().strip()
        category = self.category_var.get().strip()
        quantity_str = self.quantity_var.get().strip()
        date = self.date_var.get().strip()
        
        if not barcode:
            messagebox.showerror("خطأ", "يرجى إدخال الباركود")
            self.barcode_entry.focus()
            return False
        
        if not category:
            messagebox.showerror("خطأ", "يرجى اختيار الفئة")
            self.category_combo.focus()
            return False
        
        if not quantity_str:
            messagebox.showerror("خطأ", "يرجى إدخال الكمية")
            self.quantity_entry.focus()
            return False
        
        try:
            quantity = int(quantity_str)
            if quantity <= 0:
                raise ValueError()
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال كمية صحيحة (رقم موجب)")
            self.quantity_entry.focus()
            return False
        
        if not date:
            messagebox.showerror("خطأ", "يرجى إدخال التاريخ")
            self.date_entry.focus()
            return False
        
        # التحقق من صيغة التاريخ
        try:
            datetime.strptime(date, "%Y-%m-%d")
        except ValueError:
            messagebox.showerror("خطأ", "صيغة التاريخ غير صحيحة (YYYY-MM-DD)")
            self.date_entry.focus()
            return False
        
        return True
    
    def save_item(self):
        """حفظ المنتج"""
        if not self.validate_input():
            return
        
        try:
            barcode = self.barcode_var.get().strip()
            category_name = self.category_var.get().strip()
            quantity = int(self.quantity_var.get().strip())
            date = self.date_var.get().strip()
            
            # البحث عن معرف الفئة
            category = self.data_manager.categories.get_category_by_name(category_name)
            if not category:
                messagebox.showerror("خطأ", "الفئة المحددة غير موجودة")
                return
            
            # إضافة أو تحديث المنتج
            new_balance = self.data_manager.store_items.add_or_update_item(
                barcode, category["id"], quantity, date
            )
            
            # إضافة المعاملة
            current_time = datetime.now().strftime("%H:%M:%S")
            self.data_manager.transactions.add_transaction(
                "in", barcode, category["id"], quantity, date, current_time, new_balance
            )
            
            # رسالة نجاح
            messagebox.showinfo("نجح", f"تم إضافة {quantity} وحدة من المنتج {barcode}\nالرصيد الحالي: {new_balance}")
            
            # مسح النموذج للإدخال التالي
            self.clear_form()
            self.status_bar.config(text=f"تم حفظ المنتج {barcode} بنجاح")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ المنتج: {str(e)}")
    
    def clear_form(self):
        """مسح النموذج"""
        self.barcode_var.set("")
        self.quantity_var.set("")
        self.date_var.set(datetime.now().strftime("%Y-%m-%d"))
        self.barcode_entry.focus()
        self.status_bar.config(text="تم مسح النموذج")
    
    def winfo_exists(self):
        """التحقق من وجود النافذة"""
        try:
            return self.window.winfo_exists()
        except:
            return False
    
    def lift(self):
        """رفع النافذة للمقدمة"""
        self.window.lift()
        self.window.focus_force()

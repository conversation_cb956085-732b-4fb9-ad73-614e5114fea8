import sqlite3
import json
from datetime import datetime
from typing import List, Dict, Optional, Tuple
import os

class DatabaseManager:
    """مدير قاعدة البيانات لتطبيق إدارة المتجر"""
    
    def __init__(self, db_path: str = "store_management.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """إنشاء قاعدة البيانات والجداول"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # جدول الفئات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS categories (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT UNIQUE NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # جدول المنتجات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS store_items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    barcode TEXT UNIQUE NOT NULL,
                    category_id INTEGER,
                    balance INTEGER DEFAULT 0,
                    last_updated DATE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (category_id) REFERENCES categories (id)
                )
            ''')
            
            # جدول المعاملات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS transactions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    type TEXT NOT NULL CHECK (type IN ('in', 'out')),
                    barcode TEXT NOT NULL,
                    category_id INTEGER,
                    quantity INTEGER NOT NULL,
                    date DATE NOT NULL,
                    timestamp TIME,
                    remaining_balance INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (category_id) REFERENCES categories (id)
                )
            ''')
            
            conn.commit()
    
    def get_connection(self):
        """الحصول على اتصال بقاعدة البيانات"""
        return sqlite3.connect(self.db_path)

class Category:
    """فئة لإدارة فئات المنتجات"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
    
    def add_category(self, name: str) -> bool:
        """إضافة فئة جديدة"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("INSERT INTO categories (name) VALUES (?)", (name.strip(),))
                conn.commit()
                return True
        except sqlite3.IntegrityError:
            return False  # الفئة موجودة مسبقاً
    
    def get_all_categories(self) -> List[Dict]:
        """الحصول على جميع الفئات"""
        with self.db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT id, name FROM categories ORDER BY name")
            return [{"id": row[0], "name": row[1]} for row in cursor.fetchall()]
    
    def update_category(self, category_id: int, new_name: str) -> bool:
        """تحديث اسم فئة"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("UPDATE categories SET name = ? WHERE id = ?", 
                             (new_name.strip(), category_id))
                conn.commit()
                return cursor.rowcount > 0
        except sqlite3.IntegrityError:
            return False
    
    def delete_category(self, category_id: int) -> bool:
        """حذف فئة (إذا لم تكن مستخدمة)"""
        with self.db.get_connection() as conn:
            cursor = conn.cursor()
            # التحقق من عدم وجود منتجات في هذه الفئة
            cursor.execute("SELECT COUNT(*) FROM store_items WHERE category_id = ?", (category_id,))
            if cursor.fetchone()[0] > 0:
                return False
            
            cursor.execute("DELETE FROM categories WHERE id = ?", (category_id,))
            conn.commit()
            return cursor.rowcount > 0
    
    def get_category_by_name(self, name: str) -> Optional[Dict]:
        """البحث عن فئة بالاسم"""
        with self.db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT id, name FROM categories WHERE name = ?", (name,))
            row = cursor.fetchone()
            return {"id": row[0], "name": row[1]} if row else None

class StoreItem:
    """فئة لإدارة منتجات المتجر"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
    
    def add_or_update_item(self, barcode: str, category_id: int, quantity: int, date: str) -> bool:
        """إضافة منتج جديد أو تحديث كمية منتج موجود"""
        with self.db.get_connection() as conn:
            cursor = conn.cursor()
            
            # التحقق من وجود المنتج
            cursor.execute("SELECT id, balance FROM store_items WHERE barcode = ?", (barcode,))
            existing = cursor.fetchone()
            
            if existing:
                # تحديث المنتج الموجود
                new_balance = existing[1] + quantity
                cursor.execute("""
                    UPDATE store_items 
                    SET balance = ?, category_id = ?, last_updated = ? 
                    WHERE barcode = ?
                """, (new_balance, category_id, date, barcode))
            else:
                # إضافة منتج جديد
                cursor.execute("""
                    INSERT INTO store_items (barcode, category_id, balance, last_updated) 
                    VALUES (?, ?, ?, ?)
                """, (barcode, category_id, quantity, date))
                new_balance = quantity
            
            conn.commit()
            return new_balance
    
    def get_all_items(self) -> List[Dict]:
        """الحصول على جميع المنتجات مع أسماء الفئات"""
        with self.db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT si.barcode, c.name as category, si.balance, si.last_updated
                FROM store_items si
                JOIN categories c ON si.category_id = c.id
                ORDER BY si.barcode
            """)
            return [
                {
                    "barcode": row[0],
                    "category": row[1],
                    "balance": row[2],
                    "last_updated": row[3]
                }
                for row in cursor.fetchall()
            ]
    
    def get_item_by_barcode(self, barcode: str) -> Optional[Dict]:
        """البحث عن منتج بالباركود"""
        with self.db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT si.barcode, c.name as category, si.balance, si.last_updated, si.category_id
                FROM store_items si
                JOIN categories c ON si.category_id = c.id
                WHERE si.barcode = ?
            """, (barcode,))
            row = cursor.fetchone()
            if row:
                return {
                    "barcode": row[0],
                    "category": row[1],
                    "balance": row[2],
                    "last_updated": row[3],
                    "category_id": row[4]
                }
            return None
    
    def withdraw_item(self, barcode: str, quantity: int, date: str) -> Tuple[bool, str, int]:
        """سحب كمية من منتج"""
        with self.db.get_connection() as conn:
            cursor = conn.cursor()
            
            # البحث عن المنتج
            cursor.execute("SELECT id, balance, category_id FROM store_items WHERE barcode = ?", (barcode,))
            item = cursor.fetchone()
            
            if not item:
                return False, "المنتج غير موجود", 0
            
            if item[1] < quantity:
                return False, "الكمية المطلوبة غير متوفرة", item[1]
            
            # تحديث الرصيد
            new_balance = item[1] - quantity
            cursor.execute("""
                UPDATE store_items 
                SET balance = ?, last_updated = ? 
                WHERE barcode = ?
            """, (new_balance, date, barcode))
            
            conn.commit()
            return True, "تم السحب بنجاح", new_balance

class Transaction:
    """فئة لإدارة المعاملات"""

    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager

    def add_transaction(self, transaction_type: str, barcode: str, category_id: int,
                       quantity: int, date: str, timestamp: str, remaining_balance: int) -> bool:
        """إضافة معاملة جديدة"""
        with self.db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO transactions
                (type, barcode, category_id, quantity, date, timestamp, remaining_balance)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (transaction_type, barcode, category_id, quantity, date, timestamp, remaining_balance))
            conn.commit()
            return True

    def get_all_transactions(self) -> List[Dict]:
        """الحصول على جميع المعاملات"""
        with self.db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT t.type, t.barcode, c.name as category, t.quantity,
                       t.date, t.timestamp, t.remaining_balance
                FROM transactions t
                JOIN categories c ON t.category_id = c.id
                ORDER BY t.date DESC, t.timestamp DESC
            """)
            return [
                {
                    "type": row[0],
                    "barcode": row[1],
                    "category": row[2],
                    "quantity": row[3],
                    "date": row[4],
                    "timestamp": row[5],
                    "remaining_balance": row[6]
                }
                for row in cursor.fetchall()
            ]

    def get_monthly_report(self) -> List[Dict]:
        """إنشاء تقرير شهري"""
        with self.db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT
                    strftime('%Y-%m', t.date) as month,
                    c.name as category,
                    SUM(CASE WHEN t.type = 'in' THEN t.quantity ELSE 0 END) as added,
                    SUM(CASE WHEN t.type = 'out' THEN t.quantity ELSE 0 END) as withdrawn,
                    MAX(CASE WHEN t.type = 'in' THEN t.remaining_balance ELSE 0 END) as remaining
                FROM transactions t
                JOIN categories c ON t.category_id = c.id
                GROUP BY strftime('%Y-%m', t.date), c.name
                ORDER BY month DESC, c.name
            """)

            report_data = []
            for row in cursor.fetchall():
                month, category, added, withdrawn, remaining = row

                # حساب الرصيد السابق
                cursor.execute("""
                    SELECT remaining_balance
                    FROM transactions t
                    JOIN categories c ON t.category_id = c.id
                    WHERE c.name = ? AND strftime('%Y-%m', t.date) < ?
                    ORDER BY t.date DESC, t.timestamp DESC
                    LIMIT 1
                """, (category, month))

                prev_result = cursor.fetchone()
                previous = prev_result[0] if prev_result else 0

                total = previous + added - withdrawn

                report_data.append({
                    "month": month,
                    "category": category,
                    "added": added,
                    "withdrawn": withdrawn,
                    "remaining": remaining,
                    "previous": previous,
                    "total": total
                })

            return report_data

    def delete_transactions_by_criteria(self, criteria: str) -> int:
        """حذف المعاملات حسب معايير معينة"""
        with self.db.get_connection() as conn:
            cursor = conn.cursor()

            if criteria == "last_operation":
                # حذف آخر عملية
                cursor.execute("""
                    DELETE FROM transactions
                    WHERE (date, timestamp) = (
                        SELECT date, timestamp FROM transactions
                        ORDER BY date DESC, timestamp DESC LIMIT 1
                    )
                """)
            elif criteria == "last_hour":
                # حذف عمليات آخر ساعة
                cursor.execute("""
                    DELETE FROM transactions
                    WHERE datetime(date || ' ' || timestamp) >= datetime('now', '-1 hour')
                """)
            elif criteria == "today":
                # حذف عمليات اليوم
                cursor.execute("""
                    DELETE FROM transactions
                    WHERE date = date('now')
                """)
            elif criteria == "all":
                # حذف جميع المعاملات
                cursor.execute("DELETE FROM transactions")

            deleted_count = cursor.rowcount
            conn.commit()
            return deleted_count

class DataManager:
    """مدير البيانات الرئيسي"""

    def __init__(self, db_path: str = "store_management.db"):
        self.db_manager = DatabaseManager(db_path)
        self.categories = Category(self.db_manager)
        self.store_items = StoreItem(self.db_manager)
        self.transactions = Transaction(self.db_manager)

    def export_data(self, file_path: str) -> bool:
        """تصدير جميع البيانات إلى ملف JSON"""
        try:
            data = {
                "categories": self.categories.get_all_categories(),
                "store_items": self.store_items.get_all_items(),
                "transactions": self.transactions.get_all_transactions(),
                "export_date": datetime.now().isoformat()
            }

            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            return True
        except Exception as e:
            print(f"خطأ في تصدير البيانات: {e}")
            return False

    def import_data(self, file_path: str) -> bool:
        """استيراد البيانات من ملف JSON"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # مسح البيانات الحالية
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM transactions")
                cursor.execute("DELETE FROM store_items")
                cursor.execute("DELETE FROM categories")
                conn.commit()

            # استيراد الفئات
            for category in data.get("categories", []):
                self.categories.add_category(category["name"])

            # استيراد المنتجات
            for item in data.get("store_items", []):
                category = self.categories.get_category_by_name(item["category"])
                if category:
                    self.store_items.add_or_update_item(
                        item["barcode"],
                        category["id"],
                        item["balance"],
                        item["last_updated"]
                    )

            # استيراد المعاملات
            for transaction in data.get("transactions", []):
                category = self.categories.get_category_by_name(transaction["category"])
                if category:
                    self.transactions.add_transaction(
                        transaction["type"],
                        transaction["barcode"],
                        category["id"],
                        transaction["quantity"],
                        transaction["date"],
                        transaction["timestamp"],
                        transaction["remaining_balance"]
                    )

            return True
        except Exception as e:
            print(f"خطأ في استيراد البيانات: {e}")
            return False

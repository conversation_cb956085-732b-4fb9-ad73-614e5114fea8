import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime
import os
from database import DataManager

class MainWindow:
    """النافذة الرئيسية لتطبيق إدارة المتجر"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("نظام إدارة المتجر - Hatem Store")
        self.root.geometry("800x600")
        self.root.configure(bg='#f8f9fa')
        
        # إنشاء مدير البيانات
        self.data_manager = DataManager()
        
        # متغيرات النوافذ الفرعية
        self.store_window = None
        self.into_store_window = None
        self.out_store_window = None
        self.categories_window = None
        self.reports_window = None
        
        self.setup_ui()
        self.center_window()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان الرئيسي
        title_frame = tk.Frame(self.root, bg='#f8f9fa')
        title_frame.pack(pady=20)
        
        title_label = tk.Label(
            title_frame,
            text="نظام إدارة المتجر",
            font=("Arial", 24, "bold"),
            bg='#f8f9fa',
            fg='#2c3e50'
        )
        title_label.pack()
        
        subtitle_label = tk.Label(
            title_frame,
            text="Hatem Store Management System",
            font=("Arial", 12),
            bg='#f8f9fa',
            fg='#7f8c8d'
        )
        subtitle_label.pack()
        
        # إطار الأزرار الرئيسية
        buttons_frame = tk.Frame(self.root, bg='#f8f9fa')
        buttons_frame.pack(pady=30)
        
        # الصف الأول من الأزرار
        row1_frame = tk.Frame(buttons_frame, bg='#f8f9fa')
        row1_frame.pack(pady=10)
        
        self.create_main_button(row1_frame, "المخزون", "#3498db", self.open_store_window)
        self.create_main_button(row1_frame, "إدخال بضائع", "#27ae60", self.open_into_store_window)
        self.create_main_button(row1_frame, "إخراج بضائع", "#f39c12", self.open_out_store_window)
        
        # الصف الثاني من الأزرار
        row2_frame = tk.Frame(buttons_frame, bg='#f8f9fa')
        row2_frame.pack(pady=10)
        
        self.create_main_button(row2_frame, "الفئات", "#9b59b6", self.open_categories_window)
        self.create_main_button(row2_frame, "التقارير", "#34495e", self.open_reports_window)
        
        # الصف الثالث من الأزرار (إدارة البيانات)
        row3_frame = tk.Frame(buttons_frame, bg='#f8f9fa')
        row3_frame.pack(pady=20)
        
        self.create_main_button(row3_frame, "تصدير البيانات", "#16a085", self.export_data, width=15)
        self.create_main_button(row3_frame, "استيراد البيانات", "#2980b9", self.import_data, width=15)
        self.create_main_button(row3_frame, "إعادة تعيين", "#e74c3c", self.reset_data, width=15)
        
        # شريط الحالة
        self.status_bar = tk.Label(
            self.root,
            text="جاهز",
            relief=tk.SUNKEN,
            anchor=tk.W,
            bg='#ecf0f1',
            fg='#2c3e50'
        )
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # معلومات التطبيق
        info_frame = tk.Frame(self.root, bg='#f8f9fa')
        info_frame.pack(side=tk.BOTTOM, pady=10)
        
        info_label = tk.Label(
            info_frame,
            text="تطبيق إدارة المتجر - تم التطوير باستخدام Python",
            font=("Arial", 10),
            bg='#f8f9fa',
            fg='#95a5a6'
        )
        info_label.pack()
    
    def create_main_button(self, parent, text, color, command, width=12):
        """إنشاء زر رئيسي مع تنسيق موحد"""
        button = tk.Button(
            parent,
            text=text,
            font=("Arial", 12, "bold"),
            bg=color,
            fg='white',
            width=width,
            height=2,
            relief=tk.RAISED,
            bd=2,
            command=command,
            cursor="hand2"
        )
        button.pack(side=tk.LEFT, padx=10)
        
        # تأثيرات التفاعل
        def on_enter(e):
            button.configure(relief=tk.RAISED, bd=3)
        
        def on_leave(e):
            button.configure(relief=tk.RAISED, bd=2)
        
        button.bind("<Enter>", on_enter)
        button.bind("<Leave>", on_leave)
        
        return button
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def update_status(self, message):
        """تحديث شريط الحالة"""
        self.status_bar.config(text=message)
        self.root.update_idletasks()
    
    def open_store_window(self):
        """فتح نافذة المخزون"""
        if self.store_window is None or not self.store_window.winfo_exists():
            from store_window import StoreWindow
            self.store_window = StoreWindow(self.root, self.data_manager)
        else:
            self.store_window.lift()
    
    def open_into_store_window(self):
        """فتح نافذة إدخال البضائع"""
        if self.into_store_window is None or not self.into_store_window.winfo_exists():
            from into_store_window import IntoStoreWindow
            self.into_store_window = IntoStoreWindow(self.root, self.data_manager)
        else:
            self.into_store_window.lift()
    
    def open_out_store_window(self):
        """فتح نافذة إخراج البضائع"""
        if self.out_store_window is None or not self.out_store_window.winfo_exists():
            from out_store_window import OutStoreWindow
            self.out_store_window = OutStoreWindow(self.root, self.data_manager)
        else:
            self.out_store_window.lift()
    
    def open_categories_window(self):
        """فتح نافذة الفئات"""
        if self.categories_window is None or not self.categories_window.winfo_exists():
            from categories_window import CategoriesWindow
            self.categories_window = CategoriesWindow(self.root, self.data_manager)
        else:
            self.categories_window.lift()
    
    def open_reports_window(self):
        """فتح نافذة التقارير"""
        if self.reports_window is None or not self.reports_window.winfo_exists():
            from reports_window import ReportsWindow
            self.reports_window = ReportsWindow(self.root, self.data_manager)
        else:
            self.reports_window.lift()
    
    def export_data(self):
        """تصدير البيانات"""
        file_path = filedialog.asksaveasfilename(
            title="تصدير البيانات",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        
        if file_path:
            self.update_status("جاري تصدير البيانات...")
            if self.data_manager.export_data(file_path):
                messagebox.showinfo("نجح", "تم تصدير البيانات بنجاح")
                self.update_status("تم تصدير البيانات بنجاح")
            else:
                messagebox.showerror("خطأ", "فشل في تصدير البيانات")
                self.update_status("فشل في تصدير البيانات")
    
    def import_data(self):
        """استيراد البيانات"""
        if messagebox.askyesno("تأكيد", "هل أنت متأكد من استيراد البيانات؟ سيتم استبدال البيانات الحالية."):
            file_path = filedialog.askopenfilename(
                title="استيراد البيانات",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )
            
            if file_path:
                self.update_status("جاري استيراد البيانات...")
                if self.data_manager.import_data(file_path):
                    messagebox.showinfo("نجح", "تم استيراد البيانات بنجاح")
                    self.update_status("تم استيراد البيانات بنجاح")
                else:
                    messagebox.showerror("خطأ", "فشل في استيراد البيانات")
                    self.update_status("فشل في استيراد البيانات")
    
    def reset_data(self):
        """إعادة تعيين البيانات"""
        from reset_dialog import ResetDialog
        dialog = ResetDialog(self.root, self.data_manager)
        if dialog.result:
            self.update_status("تم إعادة تعيين البيانات")
    
    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

if __name__ == "__main__":
    app = MainWindow()
    app.run()

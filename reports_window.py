import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import csv
from datetime import datetime

class ReportsWindow:
    """نافذة التقارير"""
    
    def __init__(self, parent, data_manager):
        self.parent = parent
        self.data_manager = data_manager
        
        # إنشاء النافذة
        self.window = tk.Toplevel(parent)
        self.window.title("التقارير الشهرية")
        self.window.geometry("1000x700")
        self.window.configure(bg='#f8f9fa')
        
        # منع إغلاق النافذة الرئيسية
        self.window.transient(parent)
        
        self.setup_ui()
        self.load_report()
        self.center_window()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان
        title_frame = tk.Frame(self.window, bg='#f8f9fa')
        title_frame.pack(pady=10)
        
        title_label = tk.Label(
            title_frame,
            text="التقارير الشهرية",
            font=("Arial", 18, "bold"),
            bg='#f8f9fa',
            fg='#2c3e50'
        )
        title_label.pack()
        
        # إطار الفلترة
        filter_frame = tk.Frame(self.window, bg='#f8f9fa')
        filter_frame.pack(pady=10, padx=20, fill=tk.X)
        
        # زر التحديث
        refresh_btn = tk.Button(
            filter_frame,
            text="تحديث التقرير",
            command=self.load_report,
            bg='#3498db',
            fg='white',
            font=("Arial", 10),
            cursor="hand2"
        )
        refresh_btn.pack(side=tk.LEFT, padx=5)
        
        # معلومات التقرير
        self.info_label = tk.Label(
            filter_frame,
            text="",
            font=("Arial", 10),
            bg='#f8f9fa',
            fg='#7f8c8d'
        )
        self.info_label.pack(side=tk.RIGHT)
        
        # إطار الجدول
        table_frame = tk.Frame(self.window, bg='#f8f9fa')
        table_frame.pack(pady=10, padx=20, fill=tk.BOTH, expand=True)
        
        # إنشاء الجدول
        columns = ("الشهر", "الفئة", "الكمية المضافة", "الكمية المسحوبة", "الرصيد المتبقي", "الرصيد السابق", "الإجمالي")
        self.tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=20)
        
        # تعريف أعمدة الجدول
        for col in columns:
            self.tree.heading(col, text=col)
        
        # تحديد عرض الأعمدة
        self.tree.column("الشهر", width=100, anchor=tk.CENTER)
        self.tree.column("الفئة", width=150, anchor=tk.CENTER)
        self.tree.column("الكمية المضافة", width=120, anchor=tk.CENTER)
        self.tree.column("الكمية المسحوبة", width=120, anchor=tk.CENTER)
        self.tree.column("الرصيد المتبقي", width=120, anchor=tk.CENTER)
        self.tree.column("الرصيد السابق", width=120, anchor=tk.CENTER)
        self.tree.column("الإجمالي", width=100, anchor=tk.CENTER)
        
        # شريط التمرير العمودي
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=v_scrollbar.set)
        
        # شريط التمرير الأفقي
        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(xscrollcommand=h_scrollbar.set)
        
        # ترتيب الجدول وأشرطة التمرير
        self.tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        # تكوين الشبكة
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        # إطار الإحصائيات
        stats_frame = tk.LabelFrame(self.window, text="إحصائيات سريعة", bg='#f8f9fa', font=("Arial", 12, "bold"))
        stats_frame.pack(pady=10, padx=20, fill=tk.X)
        
        # إحصائيات في صف واحد
        stats_row = tk.Frame(stats_frame, bg='#f8f9fa')
        stats_row.pack(pady=10)
        
        self.total_added_label = tk.Label(stats_row, text="إجمالي المضاف: 0", font=("Arial", 10), bg='#f8f9fa', fg='#27ae60')
        self.total_added_label.pack(side=tk.LEFT, padx=20)
        
        self.total_withdrawn_label = tk.Label(stats_row, text="إجمالي المسحوب: 0", font=("Arial", 10), bg='#f8f9fa', fg='#e74c3c')
        self.total_withdrawn_label.pack(side=tk.LEFT, padx=20)
        
        self.total_remaining_label = tk.Label(stats_row, text="إجمالي المتبقي: 0", font=("Arial", 10), bg='#f8f9fa', fg='#3498db')
        self.total_remaining_label.pack(side=tk.LEFT, padx=20)
        
        # إطار الأزرار
        buttons_frame = tk.Frame(self.window, bg='#f8f9fa')
        buttons_frame.pack(pady=20)
        
        # زر التصدير
        export_btn = tk.Button(
            buttons_frame,
            text="تصدير إلى CSV",
            command=self.export_report,
            bg='#27ae60',
            fg='white',
            font=("Arial", 12, "bold"),
            width=15,
            height=2,
            cursor="hand2"
        )
        export_btn.pack(side=tk.LEFT, padx=10)
        
        # زر طباعة
        print_btn = tk.Button(
            buttons_frame,
            text="طباعة",
            command=self.print_report,
            bg='#9b59b6',
            fg='white',
            font=("Arial", 12, "bold"),
            width=15,
            height=2,
            cursor="hand2"
        )
        print_btn.pack(side=tk.LEFT, padx=10)
        
        # زر الإغلاق
        close_btn = tk.Button(
            buttons_frame,
            text="إغلاق",
            command=self.window.destroy,
            bg='#e74c3c',
            fg='white',
            font=("Arial", 12, "bold"),
            width=15,
            height=2,
            cursor="hand2"
        )
        close_btn.pack(side=tk.LEFT, padx=10)
        
        # شريط الحالة
        self.status_bar = tk.Label(
            self.window,
            text="جاهز",
            relief=tk.SUNKEN,
            anchor=tk.W,
            bg='#ecf0f1',
            fg='#2c3e50'
        )
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
    
    def load_report(self):
        """تحميل التقرير"""
        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # تحميل بيانات التقرير
            self.report_data = self.data_manager.transactions.get_monthly_report()
            
            # متغيرات الإحصائيات
            total_added = 0
            total_withdrawn = 0
            total_remaining = 0
            
            # عرض البيانات
            for data in self.report_data:
                # تلوين الصفوف حسب الرصيد
                tags = ()
                if data["remaining"] == 0:
                    tags = ("empty",)
                elif data["remaining"] < 10:
                    tags = ("low",)
                
                self.tree.insert("", tk.END, values=(
                    data["month"],
                    data["category"],
                    data["added"],
                    data["withdrawn"],
                    data["remaining"],
                    data["previous"],
                    data["total"]
                ), tags=tags)
                
                # تجميع الإحصائيات
                total_added += data["added"]
                total_withdrawn += data["withdrawn"]
                total_remaining += data["remaining"]
            
            # تعريف ألوان الصفوف
            self.tree.tag_configure("empty", background="#ffebee")  # أحمر فاتح
            self.tree.tag_configure("low", background="#fff3e0")    # برتقالي فاتح
            
            # تحديث الإحصائيات
            self.total_added_label.config(text=f"إجمالي المضاف: {total_added}")
            self.total_withdrawn_label.config(text=f"إجمالي المسحوب: {total_withdrawn}")
            self.total_remaining_label.config(text=f"إجمالي المتبقي: {total_remaining}")
            
            # تحديث معلومات التقرير
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.info_label.config(text=f"آخر تحديث: {current_time}")
            
            # تحديث شريط الحالة
            self.status_bar.config(text=f"تم تحميل {len(self.report_data)} سجل")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل التقرير: {str(e)}")
    
    def export_report(self):
        """تصدير التقرير إلى ملف CSV"""
        if not hasattr(self, 'report_data') or not self.report_data:
            messagebox.showwarning("تحذير", "لا توجد بيانات للتصدير")
            return
        
        file_path = filedialog.asksaveasfilename(
            title="تصدير التقرير",
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )
        
        if file_path:
            try:
                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)
                    
                    # كتابة العناوين
                    writer.writerow([
                        "الشهر", "الفئة", "الكمية المضافة", "الكمية المسحوبة", 
                        "الرصيد المتبقي", "الرصيد السابق", "الإجمالي"
                    ])
                    
                    # كتابة البيانات
                    for data in self.report_data:
                        writer.writerow([
                            data["month"],
                            data["category"],
                            data["added"],
                            data["withdrawn"],
                            data["remaining"],
                            data["previous"],
                            data["total"]
                        ])
                    
                    # إضافة سطر فارغ وإحصائيات
                    writer.writerow([])
                    writer.writerow(["إحصائيات إجمالية"])
                    writer.writerow(["إجمالي المضاف", sum(d["added"] for d in self.report_data)])
                    writer.writerow(["إجمالي المسحوب", sum(d["withdrawn"] for d in self.report_data)])
                    writer.writerow(["إجمالي المتبقي", sum(d["remaining"] for d in self.report_data)])
                
                messagebox.showinfo("نجح", f"تم تصدير التقرير إلى:\n{file_path}")
                self.status_bar.config(text="تم تصدير التقرير بنجاح")
                
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تصدير التقرير: {str(e)}")
    
    def print_report(self):
        """طباعة التقرير"""
        if not hasattr(self, 'report_data') or not self.report_data:
            messagebox.showwarning("تحذير", "لا توجد بيانات للطباعة")
            return
        
        # إنشاء نافذة معاينة الطباعة
        print_window = tk.Toplevel(self.window)
        print_window.title("معاينة الطباعة")
        print_window.geometry("800x600")
        print_window.configure(bg='white')
        
        # إنشاء نص التقرير
        text_widget = tk.Text(print_window, wrap=tk.WORD, font=("Courier", 10))
        text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # إضافة محتوى التقرير
        report_text = "تقرير المخزون الشهري\n"
        report_text += "=" * 80 + "\n\n"
        report_text += f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        
        # جدول البيانات
        report_text += f"{'الشهر':<10} {'الفئة':<20} {'مضاف':<8} {'مسحوب':<8} {'متبقي':<8} {'سابق':<8} {'إجمالي':<8}\n"
        report_text += "-" * 80 + "\n"
        
        for data in self.report_data:
            report_text += f"{data['month']:<10} {data['category']:<20} {data['added']:<8} {data['withdrawn']:<8} {data['remaining']:<8} {data['previous']:<8} {data['total']:<8}\n"
        
        # الإحصائيات
        report_text += "\n" + "=" * 80 + "\n"
        report_text += "الإحصائيات الإجمالية:\n"
        report_text += f"إجمالي المضاف: {sum(d['added'] for d in self.report_data)}\n"
        report_text += f"إجمالي المسحوب: {sum(d['withdrawn'] for d in self.report_data)}\n"
        report_text += f"إجمالي المتبقي: {sum(d['remaining'] for d in self.report_data)}\n"
        
        text_widget.insert(tk.END, report_text)
        text_widget.config(state=tk.DISABLED)
        
        # زر الطباعة (محاكاة)
        print_btn = tk.Button(
            print_window,
            text="طباعة",
            command=lambda: messagebox.showinfo("طباعة", "تم إرسال التقرير للطابعة"),
            bg='#9b59b6',
            fg='white',
            font=("Arial", 12)
        )
        print_btn.pack(pady=10)
    
    def winfo_exists(self):
        """التحقق من وجود النافذة"""
        try:
            return self.window.winfo_exists()
        except:
            return False
    
    def lift(self):
        """رفع النافذة للمقدمة"""
        self.window.lift()
        self.window.focus_force()

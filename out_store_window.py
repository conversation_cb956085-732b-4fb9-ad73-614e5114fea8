import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime

class OutStoreWindow:
    """نافذة إخراج البضائع من المتجر"""
    
    def __init__(self, parent, data_manager):
        self.parent = parent
        self.data_manager = data_manager
        
        # إنشاء النافذة
        self.window = tk.Toplevel(parent)
        self.window.title("إخراج البضائع")
        self.window.geometry("500x350")
        self.window.configure(bg='#f8f9fa')
        
        # منع إغلاق النافذة الرئيسية
        self.window.transient(parent)
        
        self.setup_ui()
        self.center_window()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان
        title_frame = tk.Frame(self.window, bg='#f8f9fa')
        title_frame.pack(pady=20)
        
        title_label = tk.Label(
            title_frame,
            text="إخراج البضائع من المتجر",
            font=("Arial", 18, "bold"),
            bg='#f8f9fa',
            fg='#2c3e50'
        )
        title_label.pack()
        
        # إطار النموذج
        form_frame = tk.Frame(self.window, bg='#f8f9fa')
        form_frame.pack(pady=20, padx=40, fill=tk.BOTH, expand=True)
        
        # الباركود
        tk.Label(form_frame, text="الباركود:", font=("Arial", 12), bg='#f8f9fa').grid(
            row=0, column=0, sticky=tk.W, pady=10
        )
        
        self.barcode_var = tk.StringVar()
        self.barcode_var.trace('w', self.on_barcode_change)
        self.barcode_entry = tk.Entry(
            form_frame, 
            textvariable=self.barcode_var,
            font=("Arial", 12),
            width=25
        )
        self.barcode_entry.grid(row=0, column=1, pady=10, padx=(10, 0), sticky=tk.W)
        self.barcode_entry.focus()
        
        # الفئة (للقراءة فقط)
        tk.Label(form_frame, text="الفئة:", font=("Arial", 12), bg='#f8f9fa').grid(
            row=1, column=0, sticky=tk.W, pady=10
        )
        
        self.category_var = tk.StringVar()
        self.category_entry = tk.Entry(
            form_frame,
            textvariable=self.category_var,
            font=("Arial", 12),
            width=25,
            state="readonly",
            bg='#ecf0f1'
        )
        self.category_entry.grid(row=1, column=1, pady=10, padx=(10, 0), sticky=tk.W)
        
        # الرصيد المتاح
        tk.Label(form_frame, text="الرصيد المتاح:", font=("Arial", 12), bg='#f8f9fa').grid(
            row=2, column=0, sticky=tk.W, pady=10
        )
        
        self.available_var = tk.StringVar()
        self.available_entry = tk.Entry(
            form_frame,
            textvariable=self.available_var,
            font=("Arial", 12),
            width=25,
            state="readonly",
            bg='#ecf0f1'
        )
        self.available_entry.grid(row=2, column=1, pady=10, padx=(10, 0), sticky=tk.W)
        
        # الكمية المطلوبة
        tk.Label(form_frame, text="الكمية المطلوبة:", font=("Arial", 12), bg='#f8f9fa').grid(
            row=3, column=0, sticky=tk.W, pady=10
        )
        
        self.quantity_var = tk.StringVar()
        self.quantity_entry = tk.Entry(
            form_frame,
            textvariable=self.quantity_var,
            font=("Arial", 12),
            width=25
        )
        self.quantity_entry.grid(row=3, column=1, pady=10, padx=(10, 0), sticky=tk.W)
        
        # إطار الأزرار
        buttons_frame = tk.Frame(self.window, bg='#f8f9fa')
        buttons_frame.pack(pady=30)
        
        # زر السحب
        withdraw_btn = tk.Button(
            buttons_frame,
            text="سحب",
            command=self.withdraw_item,
            bg='#f39c12',
            fg='white',
            font=("Arial", 14, "bold"),
            width=12,
            height=2,
            cursor="hand2"
        )
        withdraw_btn.pack(side=tk.LEFT, padx=10)
        
        # زر مسح النموذج
        clear_btn = tk.Button(
            buttons_frame,
            text="مسح",
            command=self.clear_form,
            bg='#95a5a6',
            fg='white',
            font=("Arial", 14, "bold"),
            width=12,
            height=2,
            cursor="hand2"
        )
        clear_btn.pack(side=tk.LEFT, padx=10)
        
        # زر الإغلاق
        close_btn = tk.Button(
            buttons_frame,
            text="إغلاق",
            command=self.window.destroy,
            bg='#e74c3c',
            fg='white',
            font=("Arial", 14, "bold"),
            width=12,
            height=2,
            cursor="hand2"
        )
        close_btn.pack(side=tk.LEFT, padx=10)
        
        # ربط مفتاح Enter بالسحب
        self.window.bind('<Return>', lambda event: self.withdraw_item())
        
        # شريط الحالة
        self.status_bar = tk.Label(
            self.window,
            text="أدخل الباركود للبحث عن المنتج",
            relief=tk.SUNKEN,
            anchor=tk.W,
            bg='#ecf0f1',
            fg='#2c3e50'
        )
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
    
    def on_barcode_change(self, *args):
        """عند تغيير الباركود، البحث عن المنتج تلقائياً"""
        barcode = self.barcode_var.get().strip()
        
        if not barcode:
            self.category_var.set("")
            self.available_var.set("")
            self.status_bar.config(text="أدخل الباركود للبحث عن المنتج")
            return
        
        # البحث عن المنتج
        item = self.data_manager.store_items.get_item_by_barcode(barcode)
        
        if item:
            self.category_var.set(item["category"])
            self.available_var.set(str(item["balance"]))
            
            if item["balance"] == 0:
                self.status_bar.config(text="تحذير: المنتج غير متوفر في المخزون")
                self.available_entry.config(bg='#ffebee')  # أحمر فاتح
            elif item["balance"] < 10:
                self.status_bar.config(text="تحذير: كمية قليلة متوفرة")
                self.available_entry.config(bg='#fff3e0')  # برتقالي فاتح
            else:
                self.status_bar.config(text="المنتج متوفر")
                self.available_entry.config(bg='#e8f5e8')  # أخضر فاتح
        else:
            self.category_var.set("")
            self.available_var.set("")
            self.status_bar.config(text="المنتج غير موجود في المخزون")
            self.available_entry.config(bg='#ecf0f1')  # رمادي
    
    def validate_input(self):
        """التحقق من صحة البيانات المدخلة"""
        barcode = self.barcode_var.get().strip()
        quantity_str = self.quantity_var.get().strip()
        
        if not barcode:
            messagebox.showerror("خطأ", "يرجى إدخال الباركود")
            self.barcode_entry.focus()
            return False
        
        if not quantity_str:
            messagebox.showerror("خطأ", "يرجى إدخال الكمية المطلوبة")
            self.quantity_entry.focus()
            return False
        
        try:
            quantity = int(quantity_str)
            if quantity <= 0:
                raise ValueError()
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال كمية صحيحة (رقم موجب)")
            self.quantity_entry.focus()
            return False
        
        # التحقق من وجود المنتج
        item = self.data_manager.store_items.get_item_by_barcode(barcode)
        if not item:
            messagebox.showerror("خطأ", "المنتج غير موجود في المخزون")
            self.barcode_entry.focus()
            return False
        
        # التحقق من توفر الكمية
        if item["balance"] < quantity:
            messagebox.showerror(
                "خطأ", 
                f"الكمية المطلوبة ({quantity}) أكبر من المتوفر ({item['balance']})"
            )
            self.quantity_entry.focus()
            return False
        
        return True
    
    def withdraw_item(self):
        """سحب المنتج من المخزون"""
        if not self.validate_input():
            return
        
        try:
            barcode = self.barcode_var.get().strip()
            quantity = int(self.quantity_var.get().strip())
            date = datetime.now().strftime("%Y-%m-%d")
            
            # سحب المنتج
            success, message, new_balance = self.data_manager.store_items.withdraw_item(
                barcode, quantity, date
            )
            
            if success:
                # البحث عن معرف الفئة
                item = self.data_manager.store_items.get_item_by_barcode(barcode)
                
                # إضافة المعاملة
                current_time = datetime.now().strftime("%H:%M:%S")
                self.data_manager.transactions.add_transaction(
                    "out", barcode, item["category_id"], quantity, date, current_time, new_balance
                )
                
                # رسالة نجاح
                messagebox.showinfo("نجح", f"تم سحب {quantity} وحدة من المنتج {barcode}\nالرصيد المتبقي: {new_balance}")
                
                # تحديث الرصيد المعروض
                self.available_var.set(str(new_balance))
                
                # مسح الكمية للإدخال التالي
                self.quantity_var.set("")
                self.quantity_entry.focus()
                
                self.status_bar.config(text=f"تم سحب {quantity} وحدة بنجاح")
            else:
                messagebox.showerror("خطأ", message)
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في سحب المنتج: {str(e)}")
    
    def clear_form(self):
        """مسح النموذج"""
        self.barcode_var.set("")
        self.category_var.set("")
        self.available_var.set("")
        self.quantity_var.set("")
        self.available_entry.config(bg='#ecf0f1')
        self.barcode_entry.focus()
        self.status_bar.config(text="تم مسح النموذج")
    
    def winfo_exists(self):
        """التحقق من وجود النافذة"""
        try:
            return self.window.winfo_exists()
        except:
            return False
    
    def lift(self):
        """رفع النافذة للمقدمة"""
        self.window.lift()
        self.window.focus_force()

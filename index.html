<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Store Management</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <style>
    body {
      background-color: #f8f9fa;
    }
    .main-container {
      max-width: 1200px;
      margin: 50px auto;
      padding: 20px;
      background-color: #fff;
      border-radius: 10px;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    }
    .form-container {
      display: none;
      margin-top: 20px;
    }
    .table-container {
      margin-top: 20px;
      overflow-x: auto;
    }
    .selected {
      background-color: #e9ecef;
    }
    .form-check-input {
      outline: 2px solid red;
    }
  </style>
</head>
<body>
  <div class="main-container">
    <h1 class="text-center mb-4">Store Management System</h1>
    <!-- Add this button near the other main buttons in the button-container div -->
    <div class="button-container d-flex justify-content-center gap-2 flex-wrap">
      <button class="btn btn-primary" onclick="showStore()">Store</button>
      <button class="btn btn-success" onclick="showIntoStore()">Into Store</button>
      <button class="btn btn-warning" onclick="showOutOfStore()">Out Of Store</button>
      <button class="btn btn-info" onclick="showCategories()">Categories</button>
      <button class="btn btn-secondary" onclick="showPrint()">Print Report</button>
      <button class="btn btn-primary" onclick="exportData()">Export Data</button>
      <button class="btn btn-success" onclick="importData()">Import Data</button>
      <button class="btn btn-danger" onclick="resetData()">Reset Data</button>
    </div>

    <!-- Store Form -->
    <div id="storeForm" class="form-container">
      <h2>Store Items</h2>
      <div class="table-container">
        <table class="table table-bordered table-striped" id="storeTable">
          <thead class="table-dark">
            <tr>
              <th>Barcode</th>
              <th>Category</th>
              <th>Remaining Balance</th>
              <th>Date</th>
            </tr>
          </thead>
          <tbody></tbody>
        </table>
      </div>
      <button class="btn btn-success mt-3" onclick="exportStoreItems()">Export</button>
      <button class="btn btn-danger mt-3" onclick="returnToMain()">Return</button>
    </div>

    <!-- Into Store Form -->
    <div id="intoStoreForm" class="form-container">
      <h2>Into Store</h2>
      <form id="intoStoreFormElement">
        <div class="mb-3">
          <label for="barcode" class="form-label">Barcode</label>
          <input type="text" class="form-control" id="barcode" placeholder="Enter Barcode" required>
        </div>
        <div class="mb-3">
          <label for="category" class="form-label">Category</label>
          <select class="form-select" id="category" required>
            <option value="">Select Category</option>
          </select>
        </div>
        <div class="mb-3">
          <label for="number" class="form-label">Quantity</label>
          <input type="number" class="form-control" id="number" placeholder="Enter Quantity" required>
        </div>
        <div class="mb-3">
          <label for="date" class="form-label">Date</label>
          <input type="date" class="form-control" id="date" required>
        </div>
        <button type="button" class="btn btn-success" onclick="saveIntoStore()">Save</button>
        <button type="button" class="btn btn-danger" onclick="returnToMain()">Return</button>
      </form>
    </div>

    <!-- Out Of Store Form -->
    <div id="outOfStoreForm" class="form-container">
      <h2>Out Of Store</h2>
      <form id="outOfStoreFormElement">
        <div class="mb-3">
          <label for="outBarcode" class="form-label">Barcode</label>
          <input type="text" class="form-control" id="outBarcode" placeholder="Enter Barcode" oninput="autoFillCategory()" required>
        </div>
        <div class="mb-3">
          <label for="outCategory" class="form-label">Category</label>
          <input type="text" class="form-control" id="outCategory" placeholder="Category" readonly>
        </div>
        <div class="mb-3">
          <label for="outNumber" class="form-label">Quantity</label>
          <input type="number" class="form-control" id="outNumber" placeholder="Enter Quantity" required>
        </div>
        <button type="button" class="btn btn-warning" onclick="saveOutOfStore()">Save</button>
        <button type="button" class="btn btn-danger" onclick="returnToMain()">Return</button>
      </form>
    </div>

    <!-- Categories Form -->
    <div id="categoriesForm" class="form-container">
      <h2>Categories</h2>
      <div class="table-container">
        <table class="table table-bordered table-striped" id="categoriesTable">
          <thead class="table-dark">
            <tr>
              <th>Select</th>
              <th>Category</th>
            </tr>
          </thead>
          <tbody></tbody>
        </table>
      </div>
      <button class="btn btn-primary mt-3" onclick="addCategory()">Add</button>
      <button class="btn btn-info mt-3" onclick="editSelectedCategory()">Edit</button>
      <button class="btn btn-danger mt-3" onclick="returnToMain()">Return</button>
    </div>

    <!-- Print Form -->
    <div id="printForm" class="form-container">
      <h2>Print Report</h2>
      <div class="table-container">
        <table class="table table-bordered table-striped" id="printTable">
          <thead class="table-dark">
            <tr>
              <th>Month</th>
              <th>Category</th>
              <th>Added Balance</th>
              <th>Withdrawn Balance</th>
              <th>Remaining Balance</th>
              <th>Previous Balance</th>
              <th>Total</th>                      
            </tr>
          </thead>
          <tbody></tbody>
        </table>
      </div>
      <button class="btn btn-success mt-3" onclick="exportReport()">Export</button>
      <button class="btn btn-danger mt-3" onclick="returnToMain()">Return</button>
    </div>
  </div>

  <script src="script.js"></script>

  <!-- Add this before the closing </body> tag if not already present -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <div class="modal fade" id="passwordModal" tabindex="-1" aria-labelledby="passwordModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="passwordModalLabel">Password Required</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form id="passwordForm">
            <div class="mb-3">
              <label for="adminPassword" class="form-label">Enter Admin Password</label>
              <input type="password" class="form-control" id="adminPassword" required>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="button" class="btn btn-primary" id="submitPassword">Submit</button>
        </div>
      </div>
    </div>
  </div>
</body>
</html>
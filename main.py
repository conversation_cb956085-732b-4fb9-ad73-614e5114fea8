#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق إدارة المتجر - Hatem Store Management System
تطبيق سطح مكتب لإدارة المخزون والمبيعات

المطور: تم التحويل من تطبيق ويب إلى تطبيق سطح مكتب
التاريخ: 2025
الإصدار: 1.0
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from main_window import MainWindow
    from database import DataManager
except ImportError as e:
    print(f"خطأ في استيراد الوحدات: {e}")
    sys.exit(1)

def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    try:
        # إنشاء النافذة الرئيسية
        app = MainWindow()
        
        # تشغيل التطبيق
        app.run()
        
    except Exception as e:
        # في حالة حدوث خطأ، عرض رسالة خطأ
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة الرئيسية
        
        messagebox.showerror(
            "خطأ في التطبيق",
            f"حدث خطأ أثناء تشغيل التطبيق:\n\n{str(e)}\n\nيرجى التأكد من تثبيت جميع المتطلبات."
        )
        
        sys.exit(1)

if __name__ == "__main__":
    main()

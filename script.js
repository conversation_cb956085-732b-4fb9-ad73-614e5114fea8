// Data storage
let storeItems = JSON.parse(localStorage.getItem('storeItems')) || [];
let categories = JSON.parse(localStorage.getItem('categories')) || [];
let transactions = JSON.parse(localStorage.getItem('transactions')) || [];

// Initialize date input with current date
document.addEventListener('DOMContentLoaded', function() {
  const dateInputs = document.querySelectorAll('input[type="date"]');
  const today = new Date().toISOString().split('T')[0];
  dateInputs.forEach(input => {
    input.value = today;
  });
  
  // Load categories into select
  loadCategories();
});

// Export and Import functions
async function exportData() {
  try {
    // Get all data from localStorage
    const data = {
      storeItems: storeItems,
      categories: categories,
      transactions: transactions
    };
    
    // Convert data to JSON string
    const jsonString = JSON.stringify(data, null, 2);
    
    // Check if File System Access API is supported
    if ('showSaveFilePicker' in window) {
      // Use File System Access API
      const fileHandle = await window.showSaveFilePicker({
        suggestedName: 'store-data.json',
        types: [{
          description: 'JSON Files',
          accept: {'application/json': ['.json']},
        }],
      });
      
      // Create a FileSystemWritableFileStream to write to
      const writable = await fileHandle.createWritable();
      
      // Write the contents of the file
      await writable.write(jsonString);
      
      // Close the file and write the contents to disk
      await writable.close();
      
      alert('Data exported successfully!');
    } else {
      // Fallback for browsers that don't support File System Access API
      const blob = new Blob([jsonString], {type: 'application/json'});
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'store-data.json';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      alert('Data exported successfully!');
    }
  } catch (error) {
    console.error('Error exporting data:', error);
    alert('Error exporting data: ' + error.message);
  }
}

async function importData() {
  try {
    let fileContent;
    
    // Check if File System Access API is supported
    if ('showOpenFilePicker' in window) {
      // Use File System Access API
      const [fileHandle] = await window.showOpenFilePicker({
        types: [{
          description: 'JSON Files',
          accept: {'application/json': ['.json']},
        }],
        multiple: false,
      });
      
      // Get the file
      const file = await fileHandle.getFile();
      
      // Read the file contents
      fileContent = await file.text();
    } else {
      // Fallback for browsers that don't support File System Access API
      return new Promise((resolve) => {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        
        input.onchange = (event) => {
          const file = event.target.files[0];
          if (!file) {
            alert('No file selected');
            resolve();
            return;
          }
          
          const reader = new FileReader();
          reader.onload = (e) => {
            fileContent = e.target.result;
            processImportedData(fileContent);
            resolve();
          };
          reader.readAsText(file);
        };
        
        input.click();
      });
    }
    
    if (fileContent) {
      processImportedData(fileContent);
    }
  } catch (error) {
    console.error('Error importing data:', error);
    alert('Error importing data: ' + error.message);
  }
}

function processImportedData(fileContent) {
  try {
    // Parse the JSON
    const data = JSON.parse(fileContent);
    
    // Validate the data structure
    if (!data.storeItems || !data.categories || !data.transactions) {
      throw new Error('Invalid data format');
    }
    
    // Update the data
    storeItems = data.storeItems;
    categories = data.categories;
    transactions = data.transactions;
    
    // Save to localStorage
    localStorage.setItem('storeItems', JSON.stringify(storeItems));
    localStorage.setItem('categories', JSON.stringify(categories));
    localStorage.setItem('transactions', JSON.stringify(transactions));
    
    alert('Data imported successfully!');
    
    // Refresh the current view
    const currentView = getCurrentView();
    if (currentView === 'store') showStore();
    else if (currentView === 'categories') showCategories();
    else if (currentView === 'print') showPrint();
    else if (currentView === 'intoStore') {
      showIntoStore();
      loadCategories();
    }
    else if (currentView === 'outOfStore') showOutOfStore();
  } catch (error) {
    console.error('Error processing imported data:', error);
    alert('Error processing imported data: ' + error.message);
  }
}

// Navigation functions
function showStore() {
  hideAllForms();
  document.getElementById('storeForm').style.display = 'block';
  loadStoreItems();
}

function showIntoStore() {
  hideAllForms();
  document.getElementById('intoStoreForm').style.display = 'block';
  loadCategories();
}

function showOutOfStore() {
  hideAllForms();
  document.getElementById('outOfStoreForm').style.display = 'block';
}

function showCategories() {
  hideAllForms();
  document.getElementById('categoriesForm').style.display = 'block';
  loadCategoriesTable();
}

function showPrint() {
  hideAllForms();
  document.getElementById('printForm').style.display = 'block';
  generateReport();
}

function hideAllForms() {
  const forms = document.querySelectorAll('.form-container');
  forms.forEach(form => {
    form.style.display = 'none';
  });
}

function returnToMain() {
  hideAllForms();
}

// Store Items functions
function loadStoreItems() {
  const tbody = document.querySelector('#storeTable tbody');
  tbody.innerHTML = '';
  
  storeItems.forEach(item => {
    const row = document.createElement('tr');
    row.innerHTML = `
      <td>${item.barcode}</td>
      <td>${item.category}</td>
      <td>${item.balance}</td>
      <td>${item.lastUpdated}</td>
    `;
    tbody.appendChild(row);
  });
}

function exportStoreItems() {
  if (storeItems.length === 0) {
    alert('No items to export');
    return;
  }
  
  let csv = 'Barcode,Category,Remaining Balance,Date\n';
  storeItems.forEach(item => {
    csv += `${item.barcode},${item.category},${item.balance},${item.lastUpdated}\n`;
  });
  
  const blob = new Blob([csv], { type: 'text/csv' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = 'store_items.csv';
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
}

// Into Store functions
function loadCategories() {
  const select = document.getElementById('category');
  // Clear existing options except the first one
  while (select.options.length > 1) {
    select.remove(1);
  }
  
  // Add categories
  categories.forEach(category => {
    const option = document.createElement('option');
    option.value = category.name;
    option.textContent = category.name;
    select.appendChild(option);
  });
}

function saveIntoStore() {
  const barcode = document.getElementById('barcode').value.trim();
  const category = document.getElementById('category').value;
  const quantity = parseInt(document.getElementById('number').value);
  const date = document.getElementById('date').value;
  
  if (!barcode || !category || isNaN(quantity) || quantity <= 0 || !date) {
    alert('Please fill all fields correctly');
    return;
  }
  
  // Check if item exists
  const existingItemIndex = storeItems.findIndex(item => item.barcode === barcode);
  
  if (existingItemIndex !== -1) {
    // Update existing item
    storeItems[existingItemIndex].balance += quantity;
    storeItems[existingItemIndex].lastUpdated = date;
  } else {
    // Add new item
    storeItems.push({
      barcode,
      category,
      balance: quantity,
      lastUpdated: date
    });
  }
  
  // Generate timestamp for this transaction
  const now = new Date();
  const timestamp = now.getHours().toString().padStart(2, '0') + ':' + 
                   now.getMinutes().toString().padStart(2, '0') + ':' + 
                   now.getSeconds().toString().padStart(2, '0');
  
  // Record transaction
  transactions.push({
    type: 'in',
    barcode,
    category,
    quantity,
    date,
    timestamp, // Add timestamp to identify transactions from the same operation
    remainingBalance: existingItemIndex !== -1 ? storeItems[existingItemIndex].balance : quantity
  });
  
  // Save to localStorage
  localStorage.setItem('storeItems', JSON.stringify(storeItems));
  localStorage.setItem('transactions', JSON.stringify(transactions));
  
  // Reset form
  document.getElementById('intoStoreFormElement').reset();
  document.getElementById('date').value = new Date().toISOString().split('T')[0];
  
  alert('Item added to store successfully');
}

// Out of Store functions
function autoFillCategory() {
  const barcode = document.getElementById('outBarcode').value.trim();
  const categoryInput = document.getElementById('outCategory');
  
  const item = storeItems.find(item => item.barcode === barcode);
  if (item) {
    categoryInput.value = item.category;
  } else {
    categoryInput.value = '';
  }
}

function saveOutOfStore() {
  const barcode = document.getElementById('outBarcode').value.trim();
  const quantity = parseInt(document.getElementById('outNumber').value);
  
  if (!barcode || isNaN(quantity) || quantity <= 0) {
    alert('Please fill all fields correctly');
    return;
  }
  
  // Find the item
  const itemIndex = storeItems.findIndex(item => item.barcode === barcode);
  
  if (itemIndex === -1) {
    alert('Item not found in store');
    return;
  }
  
  if (storeItems[itemIndex].balance < quantity) {
    alert('Not enough quantity in store');
    return;
  }
  
  // Update item
  storeItems[itemIndex].balance -= quantity;
  storeItems[itemIndex].lastUpdated = new Date().toISOString().split('T')[0];
  
  // Generate timestamp for this transaction
  const now = new Date();
  const timestamp = now.getHours().toString().padStart(2, '0') + ':' + 
                   now.getMinutes().toString().padStart(2, '0') + ':' + 
                   now.getSeconds().toString().padStart(2, '0');
  
  // Record transaction
  transactions.push({
    type: 'out',
    barcode,
    category: storeItems[itemIndex].category,
    quantity,
    date: new Date().toISOString().split('T')[0],
    timestamp, // Add timestamp to identify transactions from the same operation
    remainingBalance: storeItems[itemIndex].balance
  });
  
  // Save to localStorage
  localStorage.setItem('storeItems', JSON.stringify(storeItems));
  localStorage.setItem('transactions', JSON.stringify(transactions));
  
  // Reset form
  document.getElementById('outOfStoreFormElement').reset();
  
  alert('Item withdrawn from store successfully');
}

// Categories functions
function loadCategoriesTable() {
  const tbody = document.querySelector('#categoriesTable tbody');
  tbody.innerHTML = '';
  
  categories.forEach((category, index) => {
    const row = document.createElement('tr');
    row.innerHTML = `
      <td><input type="radio" name="categorySelect" value="${index}"></td>
      <td>${category.name}</td>
    `;
    tbody.appendChild(row);
  });
}

function addCategory() {
  const categoryName = prompt('Enter new category name:');
  if (categoryName && categoryName.trim()) {
    // Check if category already exists
    if (categories.some(cat => cat.name.toLowerCase() === categoryName.trim().toLowerCase())) {
      alert('Category already exists');
      return;
    }
    
    categories.push({ name: categoryName.trim() });
    localStorage.setItem('categories', JSON.stringify(categories));
    loadCategoriesTable();
    loadCategories(); // Update dropdown in Into Store form
  }
}

function editSelectedCategory() {
  const selected = document.querySelector('input[name="categorySelect"]:checked');
  if (!selected) {
    alert('Please select a category to edit');
    return;
  }
  
  const index = parseInt(selected.value);
  const newName = prompt('Enter new name for category:', categories[index].name);
  
  if (newName && newName.trim()) {
    // Check if new name already exists (except for the current category)
    if (categories.some((cat, i) => i !== index && cat.name.toLowerCase() === newName.trim().toLowerCase())) {
      alert('Category name already exists');
      return;
    }
    
    const oldName = categories[index].name;
    categories[index].name = newName.trim();
    
    // Update category name in store items
    storeItems.forEach(item => {
      if (item.category === oldName) {
        item.category = newName.trim();
      }
    });
    
    // Update category name in transactions
    transactions.forEach(transaction => {
      if (transaction.category === oldName) {
        transaction.category = newName.trim();
      }
    });
    
    localStorage.setItem('categories', JSON.stringify(categories));
    localStorage.setItem('storeItems', JSON.stringify(storeItems));
    localStorage.setItem('transactions', JSON.stringify(transactions));
    
    loadCategoriesTable();
    loadCategories(); // Update dropdown in Into Store form
  }
}

// Report functions
function generateReport() {
  const tbody = document.querySelector('#printTable tbody');
  tbody.innerHTML = '';
  
  // Group transactions by month and category
  const reportData = {};
  
  transactions.forEach(transaction => {
    const date = new Date(transaction.date);
    const month = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;
    
    if (!reportData[month]) {
      reportData[month] = {};
    }
    
    if (!reportData[month][transaction.category]) {
      reportData[month][transaction.category] = {
        added: 0,
        withdrawn: 0,
        remaining: 0,
        previous: 0
      };
    }
    
    if (transaction.type === 'in') {
      reportData[month][transaction.category].added += transaction.quantity;
    } else {
      reportData[month][transaction.category].withdrawn += transaction.quantity;
    }
    
    // The last transaction for this category in this month will have the correct remaining balance
    reportData[month][transaction.category].remaining = transaction.remainingBalance;
  });
  
  // Calculate previous balance
  const months = Object.keys(reportData).sort();
  months.forEach((month, index) => {
    const categories = Object.keys(reportData[month]);
    categories.forEach(category => {
      if (index > 0) {
        const prevMonth = months[index - 1];
        if (reportData[prevMonth] && reportData[prevMonth][category]) {
          reportData[month][category].previous = reportData[prevMonth][category].remaining;
        }
      }
    });
  });
  
  // Generate table rows
  months.forEach(month => {
    const categories = Object.keys(reportData[month]);
    categories.forEach(category => {
      const data = reportData[month][category];
      const total = data.previous + data.added - data.withdrawn;
      
      const row = document.createElement('tr');
      row.innerHTML = `
        <td>${month}</td>
        <td>${category}</td>
        <td>${data.added}</td>
        <td>${data.withdrawn}</td>
        <td>${data.remaining}</td>
        <td>${data.previous}</td>
        <td>${total}</td>
      `;
      tbody.appendChild(row);
    });
  });
}

function exportReport() {
  const table = document.getElementById('printTable');
  if (table.rows.length <= 1) {
    alert('No data to export');
    return;
  }
  
  let csv = '';
  
  // Add header row
  for (let i = 0; i < table.rows[0].cells.length; i++) {
    csv += table.rows[0].cells[i].textContent + ',';
  }
  csv = csv.slice(0, -1) + '\n';
  
  // Add data rows
  for (let i = 1; i < table.rows.length; i++) {
    for (let j = 0; j < table.rows[i].cells.length; j++) {
      csv += table.rows[i].cells[j].textContent + ',';
    }
    csv = csv.slice(0, -1) + '\n';
  }
  
  const blob = new Blob([csv], { type: 'text/csv' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = 'store_report.csv';
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
}

// Add this function to your script.js file

function resetData() {
  // Create and show the password modal
  const passwordModal = new bootstrap.Modal(document.getElementById('passwordModal'));
  passwordModal.show();
  
  // Focus on the password input
  document.getElementById('adminPassword').focus();
  
  // Handle the submit button click
  document.getElementById('submitPassword').onclick = function() {
    const password = document.getElementById('adminPassword').value;
    
    // Check if password is correct
    if (password === 'admin123') {
      // Hide the modal
      passwordModal.hide();
      
      // Clear the password field for security
      document.getElementById('adminPassword').value = '';
      
      // Ask user what to reset
      const resetOption = prompt('What would you like to reset?\n1. Last operation\n2. Last hour\n3. Today\n4. All data\n\nEnter 1, 2, 3, or 4:');
      
      if (!resetOption || !['1', '2', '3', '4'].includes(resetOption)) {
        alert('Invalid option. Reset cancelled.');
        return;
      }
      
      // Confirm reset based on selected option
      let confirmMessage = '';
      switch(resetOption) {
        case '1': confirmMessage = 'Are you sure you want to rollback the last operation?'; break;
        case '2': confirmMessage = 'Are you sure you want to rollback all operations from the last hour?'; break;
        case '3': confirmMessage = 'Are you sure you want to reset all data entered today?'; break;
        case '4': confirmMessage = 'Are you sure you want to reset ALL data? This will completely erase everything.'; break;
      }
      
      if (confirm(confirmMessage + ' This action cannot be undone.')) {
        // If option 4 is selected, reset everything
        if (resetOption === '4') {
          storeItems = [];
          categories = [];
          transactions = [];
          
          localStorage.removeItem('storeItems');
          localStorage.removeItem('categories');
          localStorage.removeItem('transactions');
          
          alert('All data has been completely reset.');
          
          // Refresh all views
          hideAllForms();
          
          // If print report was active, show it again with updated data
          if (document.getElementById('printForm').style.display === 'block') {
            showPrint();
          }
          return;
        }
        
        const today = new Date().toISOString().split('T')[0];
        const now = new Date();
        const oneHourAgo = new Date(now.getTime() - (60 * 60 * 1000));
        
        let transactionsToKeep = [];
        let transactionsToRemove = [];
        
        // Determine which transactions to keep based on the selected option
        if (resetOption === '1') {
          // Find the last transaction
          const sortedTransactions = [...transactions].sort((a, b) => 
            new Date(b.date + 'T' + (b.timestamp || '00:00:00')).getTime() - 
            new Date(a.date + 'T' + (a.timestamp || '00:00:00')).getTime()
          );
          
          if (sortedTransactions.length === 0) {
            alert('No transactions found to reset.');
            return;
          }
          
          // Get the most recent timestamp/date
          const lastTransaction = sortedTransactions[0];
          const lastTimestamp = lastTransaction.timestamp;
          const lastDate = lastTransaction.date;
          
          // Find all transactions with the same timestamp (part of the same operation)
          transactionsToRemove = transactions.filter(t => 
            (lastTimestamp && t.timestamp === lastTimestamp) || 
            (!lastTimestamp && t.date === lastDate && t === lastTransaction)
          );
          
          transactionsToKeep = transactions.filter(t => !transactionsToRemove.includes(t));
          
        } else if (resetOption === '2') {
          // Keep transactions older than one hour
          transactionsToKeep = transactions.filter(transaction => {
            const transactionDate = new Date(transaction.date + 'T' + (transaction.timestamp || '00:00:00'));
            return transactionDate < oneHourAgo;
          });
          transactionsToRemove = transactions.filter(t => !transactionsToKeep.includes(t));
        } else {
          // Keep transactions not from today
          transactionsToKeep = transactions.filter(transaction => transaction.date !== today);
          transactionsToRemove = transactions.filter(t => !transactionsToKeep.includes(t));
        }
        
        if (transactionsToRemove.length === 0) {
          alert('No data found to reset for the selected time period.');
          return;
        }
        
        // Create a map of the latest state for each barcode before the rollback
        const latestStateBeforeRollback = new Map();
        
        // Sort all transactions chronologically
        const allTransactionsSorted = [...transactions].sort((a, b) => 
          new Date(a.date + 'T' + (a.timestamp || '00:00:00')).getTime() - 
          new Date(b.date + 'T' + (b.timestamp || '00:00:00')).getTime()
        );
        
        // Build the latest state for each barcode
        allTransactionsSorted.forEach(transaction => {
          latestStateBeforeRollback.set(transaction.barcode, {
            barcode: transaction.barcode,
            category: transaction.category,
            balance: transaction.remainingBalance,
            lastUpdated: transaction.date
          });
        });
        
        // Rebuild store items from scratch based on remaining transactions
        const updatedStoreItems = [];
        const processedBarcodes = new Set();
        
        // Process transactions in chronological order
        const chronologicalTransactions = [...transactionsToKeep].sort((a, b) => 
          new Date(a.date + 'T' + (a.timestamp || '00:00:00')).getTime() - 
          new Date(b.date + 'T' + (b.timestamp || '00:00:00')).getTime()
        );
        
        // Process each transaction to rebuild the current state
        chronologicalTransactions.forEach(transaction => {
          const barcode = transaction.barcode;
          const existingItemIndex = updatedStoreItems.findIndex(item => item.barcode === barcode);
          
          if (existingItemIndex === -1) {
            // First transaction for this item
            updatedStoreItems.push({
              barcode: barcode,
              category: transaction.category,
              balance: transaction.remainingBalance,
              lastUpdated: transaction.date
            });
          } else {
            // Update existing item
            updatedStoreItems[existingItemIndex].balance = transaction.remainingBalance;
            updatedStoreItems[existingItemIndex].lastUpdated = transaction.date;
            // Ensure category is up to date
            updatedStoreItems[existingItemIndex].category = transaction.category;
          }
        });
        
        // Check for categories to keep
        const categoriesToKeep = [];
        const usedCategories = new Set();
        
        // Add categories that are used in remaining transactions
        chronologicalTransactions.forEach(t => usedCategories.add(t.category));
        
        categories.forEach(category => {
          if (usedCategories.has(category.name)) {
            categoriesToKeep.push(category);
          }
        });
        
        // Update the data
        storeItems = updatedStoreItems;
        transactions = transactionsToKeep;
        categories = categoriesToKeep;
        
        // Save to localStorage
        localStorage.setItem('storeItems', JSON.stringify(storeItems));
        localStorage.setItem('transactions', JSON.stringify(transactions));
        localStorage.setItem('categories', JSON.stringify(categories));
        
        let successMessage = '';
        switch(resetOption) {
          case '1': 
            const opType = transactionsToRemove[0]?.type === 'in' ? 'into store' : 'out of store';
            successMessage = `Last ${opType} operation has been rolled back successfully.`; 
            break;
          case '2': successMessage = 'All operations from the last hour have been rolled back successfully.'; break;
          case '3': successMessage = 'All data entered today has been reset successfully.'; break;
        }
        
        alert(successMessage);
        
        // Determine which form was active
        const storeFormActive = document.getElementById('storeForm').style.display === 'block';
        const categoriesFormActive = document.getElementById('categoriesForm').style.display === 'block';
        const printFormActive = document.getElementById('printForm').style.display === 'block';
        
        // Hide all forms first
        hideAllForms();
        
        // Reopen and refresh the active form
        if (storeFormActive) {
          showStore();
        } else if (categoriesFormActive) {
          showCategories();
        } else if (printFormActive) {
          showPrint(); // This will regenerate the report with updated data
        }
      }
    } else {
      alert('Incorrect password. Reset operation cancelled.');
      // Clear the password field
      document.getElementById('adminPassword').value = '';
    }
  };
  
  // Handle form submission (when user presses Enter)
  document.getElementById('passwordForm').onsubmit = function(e) {
    e.preventDefault();
    document.getElementById('submitPassword').click();
  };
}

// Helper function to determine which view is currently active
function getCurrentView() {
  if (document.getElementById('storeForm').style.display === 'block') return 'store';
  if (document.getElementById('categoriesForm').style.display === 'block') return 'categories';
  if (document.getElementById('printForm').style.display === 'block') return 'print';
  if (document.getElementById('intoStoreForm').style.display === 'block') return 'intoStore';
  if (document.getElementById('outOfStoreForm').style.display === 'block') return 'outOfStore';
  return '';
}
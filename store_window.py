import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import csv
from datetime import datetime

class StoreWindow:
    """نافذة عرض المخزون"""
    
    def __init__(self, parent, data_manager):
        self.parent = parent
        self.data_manager = data_manager
        
        # إنشاء النافذة
        self.window = tk.Toplevel(parent)
        self.window.title("المخزون - عرض المنتجات")
        self.window.geometry("900x600")
        self.window.configure(bg='#f8f9fa')
        
        # منع إغلاق النافذة الرئيسية
        self.window.transient(parent)
        
        self.setup_ui()
        self.load_data()
        self.center_window()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان
        title_frame = tk.Frame(self.window, bg='#f8f9fa')
        title_frame.pack(pady=10)
        
        title_label = tk.Label(
            title_frame,
            text="المخزون - عرض المنتجات",
            font=("Arial", 18, "bold"),
            bg='#f8f9fa',
            fg='#2c3e50'
        )
        title_label.pack()
        
        # إطار البحث والفلترة
        search_frame = tk.Frame(self.window, bg='#f8f9fa')
        search_frame.pack(pady=10, padx=20, fill=tk.X)
        
        tk.Label(search_frame, text="البحث:", bg='#f8f9fa', font=("Arial", 10)).pack(side=tk.LEFT)
        
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.filter_data)
        search_entry = tk.Entry(search_frame, textvariable=self.search_var, font=("Arial", 10), width=20)
        search_entry.pack(side=tk.LEFT, padx=(5, 10))
        
        tk.Label(search_frame, text="الفئة:", bg='#f8f9fa', font=("Arial", 10)).pack(side=tk.LEFT)
        
        self.category_var = tk.StringVar()
        self.category_var.trace('w', self.filter_data)
        self.category_combo = ttk.Combobox(search_frame, textvariable=self.category_var, 
                                          font=("Arial", 10), width=15, state="readonly")
        self.category_combo.pack(side=tk.LEFT, padx=(5, 10))
        
        # زر التحديث
        refresh_btn = tk.Button(
            search_frame,
            text="تحديث",
            command=self.load_data,
            bg='#3498db',
            fg='white',
            font=("Arial", 10),
            cursor="hand2"
        )
        refresh_btn.pack(side=tk.RIGHT, padx=5)
        
        # إطار الجدول
        table_frame = tk.Frame(self.window, bg='#f8f9fa')
        table_frame.pack(pady=10, padx=20, fill=tk.BOTH, expand=True)
        
        # إنشاء الجدول
        columns = ("الباركود", "الفئة", "الرصيد المتبقي", "آخر تحديث")
        self.tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)
        
        # تعريف أعمدة الجدول
        self.tree.heading("الباركود", text="الباركود")
        self.tree.heading("الفئة", text="الفئة")
        self.tree.heading("الرصيد المتبقي", text="الرصيد المتبقي")
        self.tree.heading("آخر تحديث", text="آخر تحديث")
        
        # تحديد عرض الأعمدة
        self.tree.column("الباركود", width=150, anchor=tk.CENTER)
        self.tree.column("الفئة", width=200, anchor=tk.CENTER)
        self.tree.column("الرصيد المتبقي", width=120, anchor=tk.CENTER)
        self.tree.column("آخر تحديث", width=120, anchor=tk.CENTER)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # ترتيب الجدول وشريط التمرير
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # إطار الأزرار
        buttons_frame = tk.Frame(self.window, bg='#f8f9fa')
        buttons_frame.pack(pady=20)
        
        # زر التصدير
        export_btn = tk.Button(
            buttons_frame,
            text="تصدير إلى CSV",
            command=self.export_to_csv,
            bg='#27ae60',
            fg='white',
            font=("Arial", 12, "bold"),
            width=15,
            height=2,
            cursor="hand2"
        )
        export_btn.pack(side=tk.LEFT, padx=10)
        
        # زر الإغلاق
        close_btn = tk.Button(
            buttons_frame,
            text="إغلاق",
            command=self.window.destroy,
            bg='#e74c3c',
            fg='white',
            font=("Arial", 12, "bold"),
            width=15,
            height=2,
            cursor="hand2"
        )
        close_btn.pack(side=tk.LEFT, padx=10)
        
        # شريط الحالة
        self.status_bar = tk.Label(
            self.window,
            text="جاهز",
            relief=tk.SUNKEN,
            anchor=tk.W,
            bg='#ecf0f1',
            fg='#2c3e50'
        )
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
    
    def load_data(self):
        """تحميل البيانات"""
        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # تحميل المنتجات
            self.items = self.data_manager.store_items.get_all_items()
            
            # تحميل الفئات للفلترة
            categories = self.data_manager.categories.get_all_categories()
            category_names = ["جميع الفئات"] + [cat["name"] for cat in categories]
            self.category_combo['values'] = category_names
            if not self.category_var.get():
                self.category_var.set("جميع الفئات")
            
            # عرض البيانات
            self.display_items(self.items)
            
            # تحديث شريط الحالة
            self.status_bar.config(text=f"تم تحميل {len(self.items)} منتج")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل البيانات: {str(e)}")
    
    def display_items(self, items):
        """عرض المنتجات في الجدول"""
        for item in items:
            # تلوين الصفوف حسب الرصيد
            tags = ()
            if item["balance"] == 0:
                tags = ("empty",)
            elif item["balance"] < 10:
                tags = ("low",)
            
            self.tree.insert("", tk.END, values=(
                item["barcode"],
                item["category"],
                item["balance"],
                item["last_updated"]
            ), tags=tags)
        
        # تعريف ألوان الصفوف
        self.tree.tag_configure("empty", background="#ffebee")  # أحمر فاتح للمنتجات المنتهية
        self.tree.tag_configure("low", background="#fff3e0")    # برتقالي فاتح للمنتجات القليلة
    
    def filter_data(self, *args):
        """فلترة البيانات حسب البحث والفئة"""
        if not hasattr(self, 'items'):
            return
        
        search_text = self.search_var.get().lower()
        selected_category = self.category_var.get()
        
        filtered_items = []
        for item in self.items:
            # فلترة حسب النص
            if search_text and search_text not in item["barcode"].lower() and search_text not in item["category"].lower():
                continue
            
            # فلترة حسب الفئة
            if selected_category and selected_category != "جميع الفئات" and item["category"] != selected_category:
                continue
            
            filtered_items.append(item)
        
        # مسح الجدول وعرض البيانات المفلترة
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        self.display_items(filtered_items)
        
        # تحديث شريط الحالة
        self.status_bar.config(text=f"عرض {len(filtered_items)} من {len(self.items)} منتج")
    
    def export_to_csv(self):
        """تصدير البيانات إلى ملف CSV"""
        if not hasattr(self, 'items') or not self.items:
            messagebox.showwarning("تحذير", "لا توجد بيانات للتصدير")
            return
        
        file_path = filedialog.asksaveasfilename(
            title="تصدير المخزون",
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )
        
        if file_path:
            try:
                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)
                    
                    # كتابة العناوين
                    writer.writerow(["الباركود", "الفئة", "الرصيد المتبقي", "آخر تحديث"])
                    
                    # كتابة البيانات المعروضة حالياً
                    for child in self.tree.get_children():
                        values = self.tree.item(child)['values']
                        writer.writerow(values)
                
                messagebox.showinfo("نجح", f"تم تصدير البيانات إلى:\n{file_path}")
                self.status_bar.config(text="تم تصدير البيانات بنجاح")
                
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تصدير البيانات: {str(e)}")
    
    def winfo_exists(self):
        """التحقق من وجود النافذة"""
        try:
            return self.window.winfo_exists()
        except:
            return False
    
    def lift(self):
        """رفع النافذة للمقدمة"""
        self.window.lift()
        self.window.focus_force()

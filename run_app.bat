@echo off
chcp 65001 > nul
title نظام إدارة المتجر - Hatem Store Management System

echo ========================================
echo    نظام إدارة المتجر - Hatem Store
echo    Store Management System
echo ========================================
echo.

echo جاري تشغيل التطبيق...
echo Starting application...
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Python غير مثبت على النظام
    echo Error: Python is not installed
    echo.
    echo يرجى تثبيت Python من: https://python.org
    echo Please install Python from: https://python.org
    pause
    exit /b 1
)

REM تشغيل التطبيق
python main.py

REM في حالة حدوث خطأ
if %errorlevel% neq 0 (
    echo.
    echo حدث خطأ أثناء تشغيل التطبيق
    echo An error occurred while running the application
    echo.
    pause
)

echo.
echo تم إغلاق التطبيق
echo Application closed
pause

# متطلبات تطبيق إدارة المتجر - Hatem Store Management System
# Python Desktop Application Requirements

# المكتبات الأساسية (مدمجة مع Python)
# tkinter - واجهة المستخدم الرسومية (مدمجة)
# sqlite3 - قاعدة البيانات (مدمجة)
# csv - التعامل مع ملفات CSV (مدمجة)
# json - التعامل مع ملفات JSON (مدمجة)
# datetime - التعامل مع التواريخ والأوقات (مدمجة)
# typing - تحديد أنواع البيانات (مدمجة)
# os - التعامل مع نظام التشغيل (مدمجة)

# ملاحظة: هذا التطبيق يستخدم فقط المكتبات المدمجة مع Python
# لذلك لا يحتاج إلى تثبيت مكتبات إضافية

# متطلبات النظام:
# - Python 3.6 أو أحدث
# - نظام تشغيل Windows/Linux/macOS
# - ذاكرة: 512 MB RAM كحد أدنى
# - مساحة القرص: 50 MB للتطبيق + مساحة لقاعدة البيانات

# للتطوير (اختيارية):
# pylint>=2.0.0  # فحص جودة الكود
# black>=22.0.0  # تنسيق الكود
# pytest>=6.0.0  # اختبار الوحدات

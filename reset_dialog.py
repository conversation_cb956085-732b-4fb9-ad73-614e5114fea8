import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta

class ResetDialog:
    """نافذة حوار إعادة تعيين البيانات"""
    
    def __init__(self, parent, data_manager):
        self.parent = parent
        self.data_manager = data_manager
        self.result = False
        
        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("إعادة تعيين البيانات")
        self.dialog.geometry("500x400")
        self.dialog.configure(bg='#f8f9fa')
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        self.setup_ui()
        self.center_dialog()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان
        title_frame = tk.Frame(self.dialog, bg='#f8f9fa')
        title_frame.pack(pady=20)
        
        title_label = tk.Label(
            title_frame,
            text="إعادة تعيين البيانات",
            font=("Arial", 16, "bold"),
            bg='#f8f9fa',
            fg='#e74c3c'
        )
        title_label.pack()
        
        warning_label = tk.Label(
            title_frame,
            text="تحذير: هذه العملية لا يمكن التراجع عنها!",
            font=("Arial", 10),
            bg='#f8f9fa',
            fg='#e74c3c'
        )
        warning_label.pack(pady=5)
        
        # إطار كلمة المرور
        password_frame = tk.LabelFrame(self.dialog, text="التحقق من الهوية", bg='#f8f9fa', font=("Arial", 12, "bold"))
        password_frame.pack(pady=20, padx=40, fill=tk.X)
        
        tk.Label(password_frame, text="كلمة مرور المدير:", font=("Arial", 11), bg='#f8f9fa').pack(pady=5)
        
        self.password_var = tk.StringVar()
        self.password_entry = tk.Entry(
            password_frame,
            textvariable=self.password_var,
            font=("Arial", 12),
            show="*",
            width=20
        )
        self.password_entry.pack(pady=5)
        self.password_entry.focus()
        
        # إطار خيارات الإعادة تعيين
        options_frame = tk.LabelFrame(self.dialog, text="خيارات إعادة التعيين", bg='#f8f9fa', font=("Arial", 12, "bold"))
        options_frame.pack(pady=20, padx=40, fill=tk.X)
        
        self.reset_option = tk.StringVar()
        self.reset_option.set("last_operation")
        
        # خيار آخر عملية
        tk.Radiobutton(
            options_frame,
            text="التراجع عن آخر عملية",
            variable=self.reset_option,
            value="last_operation",
            font=("Arial", 11),
            bg='#f8f9fa'
        ).pack(anchor=tk.W, pady=2)
        
        # خيار آخر ساعة
        tk.Radiobutton(
            options_frame,
            text="التراجع عن عمليات آخر ساعة",
            variable=self.reset_option,
            value="last_hour",
            font=("Arial", 11),
            bg='#f8f9fa'
        ).pack(anchor=tk.W, pady=2)
        
        # خيار اليوم
        tk.Radiobutton(
            options_frame,
            text="حذف جميع عمليات اليوم",
            variable=self.reset_option,
            value="today",
            font=("Arial", 11),
            bg='#f8f9fa'
        ).pack(anchor=tk.W, pady=2)
        
        # خيار جميع البيانات
        tk.Radiobutton(
            options_frame,
            text="حذف جميع البيانات (إعادة تعيين كاملة)",
            variable=self.reset_option,
            value="all",
            font=("Arial", 11),
            bg='#f8f9fa',
            fg='#e74c3c'
        ).pack(anchor=tk.W, pady=2)
        
        # إطار الأزرار
        buttons_frame = tk.Frame(self.dialog, bg='#f8f9fa')
        buttons_frame.pack(pady=30)
        
        # زر التنفيذ
        execute_btn = tk.Button(
            buttons_frame,
            text="تنفيذ",
            command=self.execute_reset,
            bg='#e74c3c',
            fg='white',
            font=("Arial", 12, "bold"),
            width=12,
            height=2,
            cursor="hand2"
        )
        execute_btn.pack(side=tk.LEFT, padx=10)
        
        # زر الإلغاء
        cancel_btn = tk.Button(
            buttons_frame,
            text="إلغاء",
            command=self.dialog.destroy,
            bg='#95a5a6',
            fg='white',
            font=("Arial", 12, "bold"),
            width=12,
            height=2,
            cursor="hand2"
        )
        cancel_btn.pack(side=tk.LEFT, padx=10)
        
        # ربط مفتاح Enter بالتنفيذ
        self.dialog.bind('<Return>', lambda event: self.execute_reset())
    
    def center_dialog(self):
        """توسيط النافذة"""
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f'{width}x{height}+{x}+{y}')
    
    def validate_password(self):
        """التحقق من كلمة المرور"""
        password = self.password_var.get()
        # كلمة المرور الافتراضية (يمكن تغييرها)
        admin_password = "admin123"
        
        if password != admin_password:
            messagebox.showerror("خطأ", "كلمة المرور غير صحيحة")
            self.password_entry.focus()
            return False
        
        return True
    
    def get_confirmation_message(self, option):
        """الحصول على رسالة التأكيد حسب الخيار"""
        messages = {
            "last_operation": "هل أنت متأكد من التراجع عن آخر عملية؟",
            "last_hour": "هل أنت متأكد من التراجع عن جميع العمليات في آخر ساعة؟",
            "today": "هل أنت متأكد من حذف جميع العمليات التي تمت اليوم؟",
            "all": "هل أنت متأكد من حذف جميع البيانات؟ سيتم حذف كل شيء نهائياً!"
        }
        return messages.get(option, "هل أنت متأكد من المتابعة؟")
    
    def execute_reset(self):
        """تنفيذ عملية إعادة التعيين"""
        # التحقق من كلمة المرور
        if not self.validate_password():
            return
        
        option = self.reset_option.get()
        
        # رسالة التأكيد
        confirmation_msg = self.get_confirmation_message(option)
        if not messagebox.askyesno("تأكيد", confirmation_msg + "\n\nهذه العملية لا يمكن التراجع عنها!"):
            return
        
        try:
            if option == "all":
                # حذف جميع البيانات
                self.reset_all_data()
            else:
                # حذف المعاملات حسب المعايير
                deleted_count = self.data_manager.transactions.delete_transactions_by_criteria(option)
                
                if deleted_count > 0:
                    # إعادة بناء المخزون من المعاملات المتبقية
                    self.rebuild_store_from_transactions()
                    
                    success_messages = {
                        "last_operation": f"تم التراجع عن آخر عملية بنجاح ({deleted_count} معاملة)",
                        "last_hour": f"تم التراجع عن عمليات آخر ساعة بنجاح ({deleted_count} معاملة)",
                        "today": f"تم حذف عمليات اليوم بنجاح ({deleted_count} معاملة)"
                    }
                    
                    messagebox.showinfo("نجح", success_messages.get(option, f"تم حذف {deleted_count} معاملة"))
                else:
                    messagebox.showinfo("معلومات", "لم يتم العثور على بيانات للحذف في الفترة المحددة")
            
            self.result = True
            self.dialog.destroy()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تنفيذ عملية إعادة التعيين: {str(e)}")
    
    def reset_all_data(self):
        """حذف جميع البيانات"""
        with self.data_manager.db_manager.get_connection() as conn:
            cursor = conn.cursor()
            
            # حذف جميع الجداول
            cursor.execute("DELETE FROM transactions")
            cursor.execute("DELETE FROM store_items")
            cursor.execute("DELETE FROM categories")
            
            conn.commit()
        
        messagebox.showinfo("نجح", "تم حذف جميع البيانات بنجاح")
    
    def rebuild_store_from_transactions(self):
        """إعادة بناء المخزون من المعاملات المتبقية"""
        try:
            # مسح المخزون الحالي
            with self.data_manager.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM store_items")
                conn.commit()
            
            # الحصول على جميع المعاملات المتبقية مرتبة زمنياً
            transactions = self.data_manager.transactions.get_all_transactions()
            
            # إعادة بناء المخزون
            store_state = {}  # {barcode: {category_id, balance, last_date}}
            
            for transaction in reversed(transactions):  # من الأقدم للأحدث
                barcode = transaction["barcode"]
                
                if barcode not in store_state:
                    # البحث عن معرف الفئة
                    category = self.data_manager.categories.get_category_by_name(transaction["category"])
                    if category:
                        store_state[barcode] = {
                            "category_id": category["id"],
                            "balance": 0,
                            "last_date": transaction["date"]
                        }
                
                if barcode in store_state:
                    if transaction["type"] == "in":
                        store_state[barcode]["balance"] += transaction["quantity"]
                    else:  # out
                        store_state[barcode]["balance"] -= transaction["quantity"]
                    
                    store_state[barcode]["last_date"] = transaction["date"]
            
            # إدراج البيانات المحدثة في قاعدة البيانات
            with self.data_manager.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                for barcode, data in store_state.items():
                    if data["balance"] >= 0:  # تجنب الأرصدة السالبة
                        cursor.execute("""
                            INSERT INTO store_items (barcode, category_id, balance, last_updated)
                            VALUES (?, ?, ?, ?)
                        """, (barcode, data["category_id"], data["balance"], data["last_date"]))
                
                conn.commit()
                
        except Exception as e:
            print(f"خطأ في إعادة بناء المخزون: {e}")
            raise

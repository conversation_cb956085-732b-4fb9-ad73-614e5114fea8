#!/bin/bash

# نظام إدارة المتجر - Hatem Store Management System
# Store Management System Launcher Script

echo "========================================"
echo "   نظام إدارة المتجر - Hatem Store"
echo "   Store Management System"
echo "========================================"
echo

echo "جاري تشغيل التطبيق..."
echo "Starting application..."
echo

# التحقق من وجود Python
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "خطأ: Python غير مثبت على النظام"
        echo "Error: Python is not installed"
        echo
        echo "يرجى تثبيت Python من: https://python.org"
        echo "Please install Python from: https://python.org"
        echo
        echo "في Ubuntu/Debian:"
        echo "sudo apt-get update && sudo apt-get install python3 python3-tk"
        echo
        echo "في macOS:"
        echo "brew install python-tk"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

# عرض إصدار Python
echo "Python version:"
$PYTHON_CMD --version
echo

# التحقق من وجود tkinter
$PYTHON_CMD -c "import tkinter" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "خطأ: tkinter غير مثبت"
    echo "Error: tkinter is not installed"
    echo
    echo "في Ubuntu/Debian:"
    echo "sudo apt-get install python3-tk"
    echo
    echo "في macOS:"
    echo "brew install python-tk"
    exit 1
fi

# تشغيل التطبيق
$PYTHON_CMD main.py

# التحقق من حالة الخروج
if [ $? -ne 0 ]; then
    echo
    echo "حدث خطأ أثناء تشغيل التطبيق"
    echo "An error occurred while running the application"
    echo
    read -p "اضغط Enter للمتابعة / Press Enter to continue..."
fi

echo
echo "تم إغلاق التطبيق"
echo "Application closed"

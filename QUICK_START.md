# دليل البدء السريع - نظام إدارة المتجر

## 🚀 تشغيل التطبيق

### ويندوز (Windows)
```
انقر مرتين على ملف: run_app.bat
أو افتح Command Prompt واكتب: python main.py
```

### لينكس/ماك (Linux/Mac)
```bash
./run_app.sh
# أو
python3 main.py
```

## 📋 خطوات البدء الأولى

### 1. إضافة الفئات
- انقر على زر "الفئات"
- انقر "إضافة فئة"
- أدخل اسم الفئة (مثل: إلكترونيات، ملابس، طعام)
- انقر "حفظ"

### 2. إدخال المنتجات
- انقر على زر "إدخال بضائع"
- أدخل الباركود (مثل: 123456789)
- اختر الفئة من القائمة
- أدخل الكمية (مثل: 100)
- التاريخ سيكون اليوم تلقائياً
- انقر "حفظ"

### 3. عرض المخزون
- انقر على زر "المخزون"
- ستظهر جميع المنتجات مع أرصدتها
- يمكنك البحث بالباركود أو الفئة
- انقر "تصدير إلى CSV" لحفظ البيانات

### 4. سحب المنتجات
- انقر على زر "إخراج بضائع"
- أدخل الباركود
- ستظهر معلومات المنتج تلقائياً
- أدخل الكمية المطلوبة
- انقر "سحب"

### 5. عرض التقارير
- انقر على زر "التقارير"
- ستظهر التقارير الشهرية
- يمكنك تصدير التقرير أو طباعته

## 🔧 نصائح مهمة

### الأمان
- كلمة مرور المدير: `admin123`
- استخدمها عند إعادة تعيين البيانات

### النسخ الاحتياطي
- انقر "تصدير البيانات" لحفظ نسخة احتياطية
- احفظ الملف في مكان آمن
- استخدم "استيراد البيانات" لاستعادة النسخة

### إعادة التعيين
- "التراجع عن آخر عملية": يلغي آخر إدخال أو إخراج
- "آخر ساعة": يلغي جميع العمليات في آخر ساعة
- "اليوم": يحذف جميع عمليات اليوم
- "جميع البيانات": يحذف كل شيء (احذر!)

## ❗ مشاكل شائعة

### التطبيق لا يفتح
```bash
# تأكد من تثبيت Python
python --version

# في لينكس، قد تحتاج:
sudo apt-get install python3-tk
```

### رسالة خطأ "No module named 'tkinter'"
```bash
# في Ubuntu/Debian:
sudo apt-get install python3-tk

# في CentOS/RHEL:
sudo yum install tkinter
```

### قاعدة البيانات تالفة
```bash
# احذف الملف وأعد التشغيل
rm store_management.db
python main.py
```

## 📱 اختصارات لوحة المفاتيح

- **Enter**: حفظ في النماذج
- **Escape**: إغلاق النوافذ
- **F5**: تحديث البيانات (في بعض النوافذ)

## 📊 فهم الألوان

### في المخزون والتقارير:
- **أبيض**: رصيد طبيعي
- **برتقالي فاتح**: رصيد قليل (أقل من 10)
- **أحمر فاتح**: رصيد منتهي (0)

## 🎯 نصائح للاستخدام الأمثل

1. **ابدأ بالفئات**: أضف جميع الفئات أولاً
2. **استخدم أرقام باركود واضحة**: تجنب التكرار
3. **اعمل نسخ احتياطية دورية**: كل أسبوع على الأقل
4. **راقب التقارير**: لمتابعة حركة المخزون
5. **استخدم البحث**: للعثور على المنتجات بسرعة

## 📞 في حالة المشاكل

1. أعد تشغيل التطبيق
2. تحقق من ملف README.md للتفاصيل
3. احذف قاعدة البيانات إذا كانت تالفة
4. تأكد من صلاحيات الكتابة في المجلد

---

**نصيحة**: احفظ هذا الملف للرجوع إليه عند الحاجة!

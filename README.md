# نظام إدارة المتجر - Hatem Store Management System

تطبيق سطح مكتب شامل لإدارة المخزون والمبيعات، تم تطويره باستخدام Python و Tkinter.

## 🌟 المميزات

### إدارة المخزون
- عرض جميع المنتجات مع الباركود والفئة والرصيد
- البحث والفلترة حسب الباركود أو الفئة
- تصدير بيانات المخزون إلى ملفات CSV
- تلوين المنتجات حسب حالة المخزون (منتهي، قليل، متوفر)

### إدخال البضائع
- إضافة منتجات جديدة أو زيادة كمية المنتجات الموجودة
- ربط المنتجات بالفئات
- تسجيل التاريخ والوقت للعمليات
- إضافة فئات جديدة مباشرة من النافذة

### إخراج البضائع
- سحب كميات من المخزون مع التحقق من التوفر
- ملء تلقائي لمعلومات المنتج عند إدخال الباركود
- عرض الرصيد المتاح قبل السحب
- تحذيرات للمنتجات القليلة أو المنتهية

### إدارة الفئات
- إضافة وتعديل وحذف فئات المنتجات
- واجهة سهلة لإدارة الفئات
- حماية من حذف الفئات المستخدمة

### التقارير الشهرية
- تقارير مفصلة تشمل:
  - الكميات المضافة والمسحوبة شهرياً
  - الرصيد المتبقي والسابق
  - إحصائيات إجمالية
- تصدير التقارير إلى CSV
- معاينة وطباعة التقارير

### النسخ الاحتياطي وإدارة البيانات
- تصدير جميع البيانات إلى ملف JSON
- استيراد البيانات من ملف JSON
- إعادة تعيين البيانات مع خيارات متعددة:
  - التراجع عن آخر عملية
  - التراجع عن عمليات آخر ساعة
  - حذف عمليات اليوم
  - إعادة تعيين كاملة
- حماية بكلمة مرور للعمليات الحساسة

## 🔧 متطلبات النظام

- **Python**: الإصدار 3.6 أو أحدث
- **نظام التشغيل**: Windows, Linux, أو macOS
- **الذاكرة**: 512 MB RAM كحد أدنى
- **مساحة القرص**: 50 MB للتطبيق + مساحة لقاعدة البيانات

## 📦 التثبيت والتشغيل

### 1. تحميل الملفات
```bash
# تحميل جميع ملفات التطبيق إلى مجلد واحد
git clone [repository-url]
cd hatem-store-management
```

### 2. التحقق من Python
```bash
# التحقق من إصدار Python
python --version
# أو
python3 --version
```

### 3. تشغيل التطبيق
```bash
# تشغيل التطبيق
python main.py
# أو
python3 main.py
```

## 📁 هيكل الملفات

```
hatem-store-management/
├── main.py                 # الملف الرئيسي لتشغيل التطبيق
├── main_window.py          # النافذة الرئيسية
├── database.py             # إدارة قاعدة البيانات
├── store_window.py         # نافذة عرض المخزون
├── into_store_window.py    # نافذة إدخال البضائع
├── out_store_window.py     # نافذة إخراج البضائع
├── categories_window.py    # نافذة إدارة الفئات
├── reports_window.py       # نافذة التقارير
├── reset_dialog.py         # نافذة إعادة التعيين
├── requirements.txt        # متطلبات التطبيق
├── README.md              # هذا الملف
└── store_management.db    # قاعدة البيانات (يتم إنشاؤها تلقائياً)
```

## 🚀 دليل الاستخدام

### البدء السريع
1. شغل التطبيق بتنفيذ `python main.py`
2. ستظهر النافذة الرئيسية مع الأزرار الرئيسية
3. ابدأ بإضافة الفئات من قائمة "الفئات"
4. أضف المنتجات من قائمة "إدخال بضائع"
5. راقب المخزون من قائمة "المخزون"

### إدارة الفئات
- انقر على "الفئات" لفتح نافذة إدارة الفئات
- استخدم "إضافة فئة" لإضافة فئة جديدة
- انقر مرتين على فئة لتعديلها
- استخدم "حذف فئة" لحذف فئة غير مستخدمة

### إدخال البضائع
- انقر على "إدخال بضائع"
- أدخل الباركود والفئة والكمية والتاريخ
- انقر "حفظ" لإضافة المنتج أو زيادة كميته

### إخراج البضائع
- انقر على "إخراج بضائع"
- أدخل الباركود (ستظهر معلومات المنتج تلقائياً)
- أدخل الكمية المطلوبة
- انقر "سحب" لتنفيذ العملية

### التقارير
- انقر على "التقارير" لعرض التقرير الشهري
- استخدم "تصدير إلى CSV" لحفظ التقرير
- استخدم "طباعة" لمعاينة التقرير قبل الطباعة

### النسخ الاحتياطي
- **تصدير البيانات**: احفظ جميع البيانات في ملف JSON
- **استيراد البيانات**: استرد البيانات من ملف JSON
- **إعادة تعيين**: احذف البيانات حسب الفترة المحددة

## 🔐 الأمان

- كلمة مرور المدير الافتراضية: `admin123`
- يمكن تغيير كلمة المرور من ملف `reset_dialog.py`
- جميع العمليات الحساسة محمية بكلمة مرور
- قاعدة البيانات محلية وآمنة

## 🛠️ التخصيص والتطوير

### تغيير كلمة المرور
في ملف `reset_dialog.py`، ابحث عن:
```python
admin_password = "admin123"
```
وغير القيمة إلى كلمة المرور المرغوبة.

### إضافة مميزات جديدة
- جميع الملفات منظمة ومعلقة باللغة العربية
- يمكن إضافة نوافذ جديدة بسهولة
- قاعدة البيانات قابلة للتوسع

### تحويل إلى ملف تنفيذي
```bash
# تثبيت PyInstaller
pip install pyinstaller

# إنشاء ملف تنفيذي
pyinstaller --onefile --windowed main.py
```

## 🐛 استكشاف الأخطاء

### مشاكل شائعة وحلولها

**خطأ: "No module named 'tkinter'"**
- الحل: تأكد من تثبيت Python مع tkinter
- في Ubuntu: `sudo apt-get install python3-tk`

**خطأ: "Permission denied"**
- الحل: تأكد من صلاحيات الكتابة في مجلد التطبيق

**قاعدة البيانات لا تعمل**
- الحل: احذف ملف `store_management.db` وأعد تشغيل التطبيق

**واجهة المستخدم لا تظهر بشكل صحيح**
- الحل: تأكد من دعم النظام للخطوط العربية

## 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- تحقق من ملف README هذا أولاً
- راجع التعليقات في الكود للتفاصيل التقنية
- تأكد من تحديث Python إلى أحدث إصدار

## 📄 الترخيص

هذا التطبيق مطور للاستخدام التجاري والشخصي.
يمكن تعديله وتوزيعه حسب الحاجة.

---

**تم تطوير هذا التطبيق بواسطة الذكاء الاصطناعي وتحويله من تطبيق ويب إلى تطبيق سطح مكتب**
